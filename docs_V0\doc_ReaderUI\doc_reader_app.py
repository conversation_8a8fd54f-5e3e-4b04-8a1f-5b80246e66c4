"""
Main application class for the Documentation Reader
"""

import os
import sys
from pathlib import Path
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QTreeWidget, QTreeWidgetItem, QTextEdit,
    QLabel, QStatusBar, QMenuBar, QMenu, QAction,
    QToolBar, QPushButton, QMessageBox, QFileDialog,
    QApplication, QFrame
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap, QDesktopServices
from PyQt5.QtCore import QUrl

from markdown_renderer import MarkdownRenderer
from file_explorer import FileExplorer

class DocumentationReaderApp(QMainWindow):
    """Main application window for the Documentation Reader"""

    def __init__(self, docs_root_path):
        super().__init__()

        self.docs_root = Path(docs_root_path)
        self.current_file = None

        # Initialize components
        self.markdown_renderer = MarkdownRenderer()
        self.file_explorer = FileExplorer(self.docs_root)

        # Setup UI
        self.init_ui()
        self.setup_connections()
        self.load_initial_content()

        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # Update every second

    def init_ui(self):
        """Initialize the user interface"""

        # Set window properties
        self.setWindowTitle("YouTube Explorer Documentation Reader")
        self.setGeometry(100, 100, 1400, 900)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Create header
        self.create_header(main_layout)

        # Create main content area
        self.create_main_content(main_layout)

        # Create status bar
        self.create_status_bar()

        # Create menu bar
        self.create_menu_bar()

        # Create toolbar
        self.create_toolbar()

    def create_header(self, parent_layout):
        """Create the application header"""

        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setMaximumHeight(80)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)

        # Title and description
        title_layout = QVBoxLayout()

        title_label = QLabel("📚 YouTube Explorer Documentation")
        title_font = QFont("Segoe UI", 16, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #3daee9; margin-bottom: 5px;")

        subtitle_label = QLabel("Interactive documentation browser and reader")
        subtitle_font = QFont("Segoe UI", 10)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("color: #cccccc;")

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        title_layout.addStretch()

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        # Quick action buttons
        self.create_quick_actions(header_layout)

        parent_layout.addWidget(header_frame)

    def create_quick_actions(self, parent_layout):
        """Create quick action buttons in header"""

        actions_layout = QVBoxLayout()

        # Refresh button
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.setMaximumWidth(100)
        self.refresh_btn.setToolTip("Refresh file tree")
        self.refresh_btn.clicked.connect(self.refresh_file_tree)

        # Open in external editor button
        self.external_btn = QPushButton("📝 External")
        self.external_btn.setMaximumWidth(100)
        self.external_btn.setToolTip("Open current file in external editor")
        self.external_btn.clicked.connect(self.open_external)
        self.external_btn.setEnabled(False)

        actions_layout.addWidget(self.refresh_btn)
        actions_layout.addWidget(self.external_btn)

        parent_layout.addLayout(actions_layout)

    def create_main_content(self, parent_layout):
        """Create the main content area with splitter"""

        # Create horizontal splitter
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)

        # Left panel - File explorer
        left_panel = self.create_file_panel()
        splitter.addWidget(left_panel)

        # Right panel - Content viewer
        right_panel = self.create_content_panel()
        splitter.addWidget(right_panel)

        # Set splitter proportions (30% left, 70% right)
        splitter.setSizes([400, 1000])

        parent_layout.addWidget(splitter)

    def create_file_panel(self):
        """Create the file explorer panel"""

        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMinimumWidth(250)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)

        # Panel title
        title_label = QLabel("📁 Documentation Files")
        title_font = QFont("Segoe UI", 12, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #3daee9; margin-bottom: 10px;")

        layout.addWidget(title_label)

        # File tree
        self.file_tree = self.file_explorer.create_tree_widget()
        layout.addWidget(self.file_tree)

        # File info
        self.file_info_label = QLabel("Select a file to view details")
        self.file_info_label.setStyleSheet("color: #cccccc; font-size: 9pt; margin-top: 10px;")
        self.file_info_label.setWordWrap(True)

        layout.addWidget(self.file_info_label)

        return panel

    def create_content_panel(self):
        """Create the content viewer panel"""

        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)

        # Panel title
        self.content_title = QLabel("📄 Select a document to preview")
        title_font = QFont("Segoe UI", 12, QFont.Bold)
        self.content_title.setFont(title_font)
        self.content_title.setStyleSheet("color: #3daee9; margin-bottom: 10px;")

        layout.addWidget(self.content_title)

        # Content viewer
        self.content_viewer = QTextEdit()
        self.content_viewer.setReadOnly(True)
        self.content_viewer.setPlainText("Welcome to the YouTube Explorer Documentation Reader!\n\n"
                                       "Select a markdown file from the left panel to preview its content here.\n\n"
                                       "Features:\n"
                                       "• Interactive file tree navigation\n"
                                       "• Markdown rendering with syntax highlighting\n"
                                       "• File information and statistics\n"
                                       "• External editor integration\n"
                                       "• Modern dark theme interface")

        layout.addWidget(self.content_viewer)

        return panel

    def create_status_bar(self):
        """Create the status bar"""

        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # Status labels
        self.status_label = QLabel("Ready")
        self.file_count_label = QLabel("")
        self.current_file_label = QLabel("")

        self.status_bar.addWidget(self.status_label)
        self.status_bar.addPermanentWidget(self.file_count_label)
        self.status_bar.addPermanentWidget(self.current_file_label)

        self.update_status()

    def create_menu_bar(self):
        """Create the menu bar"""

        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('&File')

        refresh_action = QAction('&Refresh', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_file_tree)
        file_menu.addAction(refresh_action)

        file_menu.addSeparator()

        exit_action = QAction('E&xit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # View menu
        view_menu = menubar.addMenu('&View')

        external_action = QAction('Open in &External Editor', self)
        external_action.setShortcut('Ctrl+E')
        external_action.triggered.connect(self.open_external)
        view_menu.addAction(external_action)

        # Help menu
        help_menu = menubar.addMenu('&Help')

        about_action = QAction('&About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """Create the toolbar"""

        toolbar = self.addToolBar('Main')
        toolbar.setMovable(False)

        # Add actions to toolbar
        refresh_action = QAction('🔄', self)
        refresh_action.setToolTip('Refresh file tree (F5)')
        refresh_action.triggered.connect(self.refresh_file_tree)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        external_action = QAction('📝', self)
        external_action.setToolTip('Open in external editor (Ctrl+E)')
        external_action.triggered.connect(self.open_external)
        toolbar.addAction(external_action)

    def setup_connections(self):
        """Setup signal connections"""

        # File tree selection
        self.file_tree.itemClicked.connect(self.on_file_selected)
        self.file_tree.itemDoubleClicked.connect(self.on_file_double_clicked)

    def load_initial_content(self):
        """Load initial content"""

        # Populate file tree
        self.file_explorer.populate_tree(self.file_tree)

        # Try to load README.md by default
        readme_path = self.docs_root / "README.md"
        if readme_path.exists():
            self.load_file(readme_path)

    def on_file_selected(self, item, column):
        """Handle file selection in tree"""

        file_path = item.data(0, Qt.UserRole)
        if file_path and Path(file_path).is_file():
            self.load_file(Path(file_path))
            self.update_file_info(Path(file_path))

    def on_file_double_clicked(self, item, column):
        """Handle double-click on file"""

        file_path = item.data(0, Qt.UserRole)
        if file_path and Path(file_path).is_file():
            self.open_external()

    def load_file(self, file_path):
        """Load and display a file"""

        try:
            self.current_file = file_path

            # Update title
            self.content_title.setText(f"📄 {file_path.name}")

            # Load and render content
            if file_path.suffix.lower() == '.md':
                html_content = self.markdown_renderer.render_file(file_path)
                self.content_viewer.setHtml(html_content)
            else:
                # Plain text for non-markdown files
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.content_viewer.setPlainText(content)

            # Enable external editor button
            self.external_btn.setEnabled(True)

            # Update status
            self.status_label.setText(f"Loaded: {file_path.name}")

        except Exception as e:
            self.show_error(f"Error loading file: {str(e)}")

    def update_file_info(self, file_path):
        """Update file information display"""

        try:
            stat = file_path.stat()
            size = stat.st_size

            # Format size
            if size < 1024:
                size_str = f"{size} bytes"
            elif size < 1024 * 1024:
                size_str = f"{size / 1024:.1f} KB"
            else:
                size_str = f"{size / (1024 * 1024):.1f} MB"

            # Count lines for text files
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                line_info = f" • {lines} lines"
            except:
                line_info = ""

            info_text = f"📊 {file_path.name}\n{size_str}{line_info}"
            self.file_info_label.setText(info_text)

        except Exception as e:
            self.file_info_label.setText(f"Error reading file info: {str(e)}")

    def refresh_file_tree(self):
        """Refresh the file tree"""

        self.file_explorer.populate_tree(self.file_tree)
        self.status_label.setText("File tree refreshed")

    def open_external(self):
        """Open current file in external editor"""

        if self.current_file:
            try:
                QDesktopServices.openUrl(QUrl.fromLocalFile(str(self.current_file)))
                self.status_label.setText(f"Opened {self.current_file.name} externally")
            except Exception as e:
                self.show_error(f"Error opening external editor: {str(e)}")

    def update_status(self):
        """Update status bar information"""

        # Count files
        md_files = list(self.docs_root.glob("*.md"))
        self.file_count_label.setText(f"📄 {len(md_files)} docs")

        # Current file info
        if self.current_file:
            self.current_file_label.setText(f"📂 {self.current_file.name}")
        else:
            self.current_file_label.setText("")

    def show_about(self):
        """Show about dialog"""

        QMessageBox.about(self, "About Documentation Reader",
                         "YouTube Explorer Documentation Reader v1.0\n\n"
                         "A beautiful Qt-based dashboard for exploring\n"
                         "and previewing project documentation.\n\n"
                         "Built with PyQt5 and Python")

    def show_error(self, message):
        """Show error message"""

        QMessageBox.critical(self, "Error", message)
        self.status_label.setText("Error occurred")

    def center_on_screen(self):
        """Center the window on screen"""

        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def closeEvent(self, event):
        """Handle application close event"""

        reply = QMessageBox.question(self, 'Exit Documentation Reader',
                                   'Are you sure you want to exit?',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
