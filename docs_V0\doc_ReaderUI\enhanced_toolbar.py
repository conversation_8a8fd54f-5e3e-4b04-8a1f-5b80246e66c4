"""
Enhanced Toolbar for Documentation Reader
Organized controls with tabs and groups
"""

from PyQt5.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QTabWidget,
    QPushButton, QComboBox, QSpinBox, QLabel,
    QFrame, QButtonGroup, QGroupBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

class EnhancedToolbar(QWidget):
    """Enhanced toolbar with organized controls"""

    # Signals
    theme_changed = pyqtSignal(str)
    font_size_changed = pyqtSignal(int)
    expand_all_requested = pyqtSignal()
    collapse_all_requested = pyqtSignal()
    refresh_requested = pyqtSignal()
    external_editor_requested = pyqtSignal()

    def __init__(self, settings_manager):
        super().__init__()
        self.settings_manager = settings_manager
        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """Initialize the toolbar UI"""

        # Set fixed height for the entire toolbar widget
        self.setMaximumHeight(60)
        self.setMinimumHeight(55)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(2, 1, 2, 1)
        main_layout.setSpacing(1)

        # Create tab widget for organized controls
        self.tab_widget = QTabWidget()
        self.tab_widget.setMaximumHeight(45)
        self.tab_widget.setMinimumHeight(40)

        # Create tabs
        self.create_file_tab()
        self.create_view_tab()
        self.create_settings_tab()

        main_layout.addWidget(self.tab_widget)

    def create_file_tab(self):
        """Create file operations tab"""

        file_tab = QWidget()
        layout = QHBoxLayout(file_tab)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(5)

        # Refresh button
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.setToolTip("Refresh file tree (F5)")
        self.refresh_btn.setMaximumWidth(70)
        self.refresh_btn.setMaximumHeight(20)

        # External editor button
        self.external_btn = QPushButton("📝 External")
        self.external_btn.setToolTip("Open current file in external editor (Ctrl+E)")
        self.external_btn.setMaximumWidth(70)
        self.external_btn.setMaximumHeight(20)
        self.external_btn.setEnabled(False)

        # Expand all button
        self.expand_all_btn = QPushButton("📂 Expand All")
        self.expand_all_btn.setToolTip("Expand all folders in file tree")
        self.expand_all_btn.setMaximumWidth(90)
        self.expand_all_btn.setMaximumHeight(20)

        # Collapse all button
        self.collapse_all_btn = QPushButton("📁 Collapse All")
        self.collapse_all_btn.setToolTip("Collapse all folders in file tree")
        self.collapse_all_btn.setMaximumWidth(90)
        self.collapse_all_btn.setMaximumHeight(20)

        # Add buttons to layout
        layout.addWidget(self.refresh_btn)
        layout.addWidget(self.external_btn)
        layout.addWidget(self.expand_all_btn)
        layout.addWidget(self.collapse_all_btn)
        layout.addStretch()

        self.tab_widget.addTab(file_tab, "📁 Files")

    def create_view_tab(self):
        """Create view settings tab"""

        view_tab = QWidget()
        layout = QHBoxLayout(view_tab)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(5)

        # Theme controls
        theme_label = QLabel("Theme:")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Dark", "Light", "Auto"])
        self.theme_combo.setCurrentText(self.settings_manager.current_theme.title())
        self.theme_combo.setMaximumWidth(70)
        self.theme_combo.setMaximumHeight(20)

        # Font size controls
        explorer_label = QLabel("Explorer:")
        self.explorer_font_spin = QSpinBox()
        self.explorer_font_spin.setRange(10, 24)
        self.explorer_font_spin.setValue(self.settings_manager.explorer_font_size)
        self.explorer_font_spin.setSuffix("px")
        self.explorer_font_spin.setMaximumWidth(60)
        self.explorer_font_spin.setMaximumHeight(20)

        content_label = QLabel("Content:")
        self.content_font_spin = QSpinBox()
        self.content_font_spin.setRange(12, 28)
        self.content_font_spin.setValue(self.settings_manager.content_font_size)
        self.content_font_spin.setSuffix("px")
        self.content_font_spin.setMaximumWidth(60)
        self.content_font_spin.setMaximumHeight(20)

        # Add controls to layout
        layout.addWidget(theme_label)
        layout.addWidget(self.theme_combo)
        layout.addWidget(explorer_label)
        layout.addWidget(self.explorer_font_spin)
        layout.addWidget(content_label)
        layout.addWidget(self.content_font_spin)
        layout.addStretch()

        self.tab_widget.addTab(view_tab, "🎨 View")

    def create_settings_tab(self):
        """Create settings tab"""

        settings_tab = QWidget()
        layout = QHBoxLayout(settings_tab)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(5)

        # Reset settings button
        self.reset_btn = QPushButton("🔄 Reset")
        self.reset_btn.setToolTip("Reset all settings to defaults")
        self.reset_btn.setMaximumWidth(70)
        self.reset_btn.setMaximumHeight(20)

        # Save settings button
        self.save_btn = QPushButton("💾 Save")
        self.save_btn.setToolTip("Save current settings")
        self.save_btn.setMaximumWidth(70)
        self.save_btn.setMaximumHeight(20)

        # Info label
        self.info_label = QLabel("Documentation Reader v1.0")
        self.info_label.setStyleSheet("color: #888888; font-size: 10px;")

        # Add controls to layout
        layout.addWidget(self.reset_btn)
        layout.addWidget(self.save_btn)
        layout.addWidget(self.info_label)
        layout.addStretch()

        self.tab_widget.addTab(settings_tab, "⚙️ Settings")

    def setup_connections(self):
        """Setup signal connections"""

        # File operations
        self.refresh_btn.clicked.connect(self.refresh_requested.emit)
        self.external_btn.clicked.connect(self.external_editor_requested.emit)

        # Tree operations
        self.expand_all_btn.clicked.connect(self.expand_all_requested.emit)
        self.collapse_all_btn.clicked.connect(self.collapse_all_requested.emit)

        # View settings
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        self.explorer_font_spin.valueChanged.connect(self.on_explorer_font_changed)
        self.content_font_spin.valueChanged.connect(self.on_content_font_changed)

        # Settings
        self.reset_btn.clicked.connect(self.reset_settings)
        self.save_btn.clicked.connect(self.save_settings)

    def on_theme_changed(self, theme_text):
        """Handle theme change"""
        theme_name = theme_text.lower()
        self.settings_manager.set_theme(theme_name)
        self.theme_changed.emit(theme_name)

    def on_explorer_font_changed(self, size):
        """Handle explorer font size change"""
        self.settings_manager.set_explorer_font_size(size)
        self.font_size_changed.emit(size)

    def on_content_font_changed(self, size):
        """Handle content font size change"""
        self.settings_manager.set_content_font_size(size)

    def reset_settings(self):
        """Reset all settings to defaults"""
        self.theme_combo.setCurrentText("Dark")
        self.explorer_font_spin.setValue(15)
        self.content_font_spin.setValue(16)

        self.settings_manager.set_theme("dark")
        self.settings_manager.set_explorer_font_size(15)
        self.settings_manager.set_content_font_size(16)

        self.theme_changed.emit("dark")
        self.font_size_changed.emit(15)

    def save_settings(self):
        """Save current settings"""
        self.settings_manager.save_settings()
        self.info_label.setText("Settings saved successfully!")

    def set_external_button_enabled(self, enabled):
        """Enable/disable external editor button"""
        self.external_btn.setEnabled(enabled)

    def update_info(self, message):
        """Update info label"""
        self.info_label.setText(message)
