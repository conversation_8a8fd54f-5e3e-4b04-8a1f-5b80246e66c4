[MASTER]
# Use multiple processes to speed up Pylint
jobs=4

# List of plugins
load-plugins=

# Python code to execute
init-hook='import sys; sys.path.append(".")'

[MESSAGES CONTROL]
# Disable specific warnings
disable=C0111,  # missing-docstring
        C0103,  # invalid-name
        W0511,  # fixme
        R0903   # too-few-public-methods

[REPORTS]
# Set the output format
output-format=text

# Include a brief explanation of each error
msg-template={path}:{line}: [{msg_id}({symbol}), {obj}] {msg}

[FORMAT]
# Maximum number of characters on a single line
max-line-length=100

[BASIC]
# Regular expression for allowed names
good-names=i,j,k,ex,Run,_

[LOGGING]
# Logging modules to check that string format arguments are in logging function parameter format
logging-modules=logging

[MISCELLANEOUS]
# List of note tags to take in consideration
notes=FIXME,XXX,TODO
