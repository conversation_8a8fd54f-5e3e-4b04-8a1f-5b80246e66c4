{"timestamp": "2025-06-04T19:03:07.258782", "response": {"kind": "youtube#playlistItemListResponse", "etag": "hqYmrz3WGa5rFsehjsIAVii5aPY", "items": [{"kind": "youtube#playlistItem", "etag": "oE5P-yFFcesIJyGNxSIwzgJ2iDg", "id": "UExNTnE0Q0duWFQzNW1MZUtGdWxuSWVMSlluSkZhaEZQbS41NkI0NEY2RDEwNTU3Q0M2", "snippet": {"publishedAt": "2024-02-07T10:31:11Z", "channelId": "UCEtWIysVwp6s7spxA7uTD2g", "title": "Advanced Work Packaging (AWP) with AVEVA E3D Design", "description": "Work Packs enable designers to think and plan for the installation and construction of the project very early on in the design phase, creating practical, staged Work Packs of design elements for construction teams.\n\nLearn more about AVEVA E3D Design\nhttps://www.aveva.com/en/products/e3d-design/", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/LrtFKS2mY0w/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/LrtFKS2mY0w/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/LrtFKS2mY0w/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/LrtFKS2mY0w/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/LrtFKS2mY0w/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "AVEVA Group", "playlistId": "PLMNq4CGnXT35mLeKFulnIeLJYnJFahFPm", "position": 0, "resourceId": {"kind": "youtube#video", "videoId": "LrtFKS2mY0w"}, "videoOwnerChannelTitle": "AVEVA Group", "videoOwnerChannelId": "UCEtWIysVwp6s7spxA7uTD2g"}}, {"kind": "youtube#playlistItem", "etag": "S4a1p3xl5O4uhW36DLUuDZF2fGI", "id": "UExNTnE0Q0duWFQzNW1MZUtGdWxuSWVMSlluSkZhaEZQbS4yODlGNEE0NkRGMEEzMEQy", "snippet": {"publishedAt": "2024-02-07T10:35:01Z", "channelId": "UCEtWIysVwp6s7spxA7uTD2g", "title": "AVEVA E3D Design MultiCAD Feature", "description": "The MultiCAD feature for AVEVA E3D Design enables the import and processing of files from over 30 different CAD software systems, including AutoCAD, Creo, Solidworks, and Revit. Import of CAD files can be executed either directly from the model module user-interface via the Interfaces tab, or from the command line or a PML Object, including in batch mode.\n\nLearn more about AVEVA E3D Design https://www.aveva.com/en/products/e3d-design/", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/-9UzdRuW3lE/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/-9UzdRuW3lE/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/-9UzdRuW3lE/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/-9UzdRuW3lE/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/-9UzdRuW3lE/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "AVEVA Group", "playlistId": "PLMNq4CGnXT35mLeKFulnIeLJYnJFahFPm", "position": 1, "resourceId": {"kind": "youtube#video", "videoId": "-9UzdRuW3lE"}, "videoOwnerChannelTitle": "AVEVA Group", "videoOwnerChannelId": "UCEtWIysVwp6s7spxA7uTD2g"}}, {"kind": "youtube#playlistItem", "etag": "FMYnlpLeRnDdyuQdDXJUMrag5DA", "id": "UExNTnE0Q0duWFQzNW1MZUtGdWxuSWVMSlluSkZhaEZQbS4wMTcyMDhGQUE4NTIzM0Y5", "snippet": {"publishedAt": "2024-02-07T10:37:36Z", "channelId": "UCEtWIysVwp6s7spxA7uTD2g", "title": "AVEVA E3D Design Space Management Application", "description": "The Space Management application in AVEVA E3D Design introduces the capability to create Functional Arrangements, Space Arrangements, Area Arrangements and Curve Arrangements.\n\nSpace Arrangements, such as Compartment or Zone Arrangements, provide essential design information with volumetric representation for Ship or other type of projects which also enables 3D Coordination capability for a project.\n\nLearn more about AVEVA E3D Design\nhttps://www.aveva.com/en/products/e3d-design/", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/jd2OLctPn0o/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/jd2OLctPn0o/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/jd2OLctPn0o/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/jd2OLctPn0o/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/jd2OLctPn0o/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "AVEVA Group", "playlistId": "PLMNq4CGnXT35mLeKFulnIeLJYnJFahFPm", "position": 2, "resourceId": {"kind": "youtube#video", "videoId": "jd2OLctPn0o"}, "videoOwnerChannelTitle": "AVEVA Group", "videoOwnerChannelId": "UCEtWIysVwp6s7spxA7uTD2g"}}, {"kind": "youtube#playlistItem", "etag": "LKPeaJX03q1fICpFJWWiYG6Ntp0", "id": "UExNTnE0Q0duWFQzNW1MZUtGdWxuSWVMSlluSkZhaEZQbS41MjE1MkI0OTQ2QzJGNzNG", "snippet": {"publishedAt": "2024-02-07T10:38:49Z", "channelId": "UCEtWIysVwp6s7spxA7uTD2g", "title": "AVEVA E3D Design Structures Feature Update", "description": "Feature enhancements to the existing End Datum Connection functionality allowing the connection of a Section to a plate or floor.\n\nLearn more about AVEVA E3D Design\nhttps://www.aveva.com/en/products/e3d-design/", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/GHGxyjcgq9A/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/GHGxyjcgq9A/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/GHGxyjcgq9A/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/GHGxyjcgq9A/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/GHGxyjcgq9A/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "AVEVA Group", "playlistId": "PLMNq4CGnXT35mLeKFulnIeLJYnJFahFPm", "position": 3, "resourceId": {"kind": "youtube#video", "videoId": "GHGxyjcgq9A"}, "videoOwnerChannelTitle": "AVEVA Group", "videoOwnerChannelId": "UCEtWIysVwp6s7spxA7uTD2g"}}, {"kind": "youtube#playlistItem", "etag": "SWnoitbD7vAjDgBB-RtXjGqODxc", "id": "UExNTnE0Q0duWFQzNW1MZUtGdWxuSWVMSlluSkZhaEZQbS4wOTA3OTZBNzVEMTUzOTMy", "snippet": {"publishedAt": "2024-02-07T10:45:22Z", "channelId": "UCEtWIysVwp6s7spxA7uTD2g", "title": "AVEVA E3D Design Graphical Explorer Feature", "description": "Introduction of the Graphical Explorer to AVEVA E3D Design for model navigation and reference. The Graphical Explorer improves performance when rendering large models, with model elements loaded from a generated cache using AVEVA’s proprietary rendering framework.\n\nLearn more about AVEVA E3D Design\nhttps://www.aveva.com/en/products/e3d-design/", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/TrMeUc7Qtk8/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/TrMeUc7Qtk8/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/TrMeUc7Qtk8/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/TrMeUc7Qtk8/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/TrMeUc7Qtk8/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "AVEVA Group", "playlistId": "PLMNq4CGnXT35mLeKFulnIeLJYnJFahFPm", "position": 4, "resourceId": {"kind": "youtube#video", "videoId": "TrMeUc7Qtk8"}, "videoOwnerChannelTitle": "AVEVA Group", "videoOwnerChannelId": "UCEtWIysVwp6s7spxA7uTD2g"}}, {"kind": "youtube#playlistItem", "etag": "VyNjq2u2P2Fwx6PF2-xZDL_3sPs", "id": "UExNTnE0Q0duWFQzNW1MZUtGdWxuSWVMSlluSkZhaEZQbS4xMkVGQjNCMUM1N0RFNEUx", "snippet": {"publishedAt": "2024-02-07T10:45:37Z", "channelId": "UCEtWIysVwp6s7spxA7uTD2g", "title": "AVEVA E3D Design AI/ML Module", "description": "AVEVA E3D Design has the ability to import trained machine learning models with Microsoft ML.NET Models into AVEVA PML. Import of a Microsoft ML.NET Model can be executed from the Model module user interface, via the Machine Learning button in the Developer tab.\n\nLearn more about AVEVA E3D Design\nhttps://www.aveva.com/en/products/e3d-design/", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/bfZtTFktPzo/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/bfZtTFktPzo/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/bfZtTFktPzo/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/bfZtTFktPzo/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/bfZtTFktPzo/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "AVEVA Group", "playlistId": "PLMNq4CGnXT35mLeKFulnIeLJYnJFahFPm", "position": 5, "resourceId": {"kind": "youtube#video", "videoId": "bfZtTFktPzo"}, "videoOwnerChannelTitle": "AVEVA Group", "videoOwnerChannelId": "UCEtWIysVwp6s7spxA7uTD2g"}}, {"kind": "youtube#playlistItem", "etag": "DWbBFibtPg12OTacOOnPugYDOq8", "id": "UExNTnE0Q0duWFQzNW1MZUtGdWxuSWVMSlluSkZhaEZQbS41MzJCQjBCNDIyRkJDN0VD", "snippet": {"publishedAt": "2024-02-07T10:45:46Z", "channelId": "UCEtWIysVwp6s7spxA7uTD2g", "title": "AVEVA E3D Design Supports Feature Update", "description": "Enable the positioning of supports in relation to specific orthogonal plane directions. This enhancement is particularly useful for precisely locating a support on sloping lines in relation to other modelled items, for example alignment along plane and alignment in horizontal plane of items.\n\nLearn more about AVEVA E3D Design\nhttps://www.aveva.com/en/products/e3d-design", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/wgC7pLwAo2A/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/wgC7pLwAo2A/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/wgC7pLwAo2A/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/wgC7pLwAo2A/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/wgC7pLwAo2A/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "AVEVA Group", "playlistId": "PLMNq4CGnXT35mLeKFulnIeLJYnJFahFPm", "position": 6, "resourceId": {"kind": "youtube#video", "videoId": "wgC7pLwAo2A"}, "videoOwnerChannelTitle": "AVEVA Group", "videoOwnerChannelId": "UCEtWIysVwp6s7spxA7uTD2g"}}, {"kind": "youtube#playlistItem", "etag": "A35qRJp6MiNxzKvuTDmHwc71_Rs", "id": "UExNTnE0Q0duWFQzNW1MZUtGdWxuSWVMSlluSkZhaEZQbS45NDk1REZENzhEMzU5MDQz", "snippet": {"publishedAt": "2024-02-08T14:54:56Z", "channelId": "UCEtWIysVwp6s7spxA7uTD2g", "title": "AVEVA E3D Design Rule Manager feature", "description": "The Rule Manager of AVEVA E3D Design allows administrators to configure logical rules for a project. These rules can be executed as design elements are created, automatically setting attributes and properties; speeding up the design process, reducing errors, and ensuring that created elements are compliant with relevant standards and design intent.\n\nLearn more about AVEVA E3D Design\nhttps://www.aveva.com/en/products/e3d-design/", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/6rtgt_VkWXg/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/6rtgt_VkWXg/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/6rtgt_VkWXg/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/6rtgt_VkWXg/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/6rtgt_VkWXg/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "AVEVA Group", "playlistId": "PLMNq4CGnXT35mLeKFulnIeLJYnJFahFPm", "position": 7, "resourceId": {"kind": "youtube#video", "videoId": "6rtgt_VkWXg"}, "videoOwnerChannelTitle": "AVEVA Group", "videoOwnerChannelId": "UCEtWIysVwp6s7spxA7uTD2g"}}], "pageInfo": {"totalResults": 8, "resultsPerPage": 10}}}