{"timestamp": "2025-06-17T09:56:54.166237", "response": {"kind": "youtube#playlistItemListResponse", "etag": "SxsKdl9nu1giIYab8ZJ9Rc756nA", "nextPageToken": "EAAaHlBUOkNBb2lFRFEzTmtJd1JFTXlOVVEzUkVWRk9FRQ", "items": [{"kind": "youtube#playlistItem", "etag": "IVdXrfOUIlw9gHTbq8tHRlE2ZVw", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC41NkI0NEY2RDEwNTU3Q0M2", "snippet": {"publishedAt": "2021-11-18T07:44:31Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "E3d Equipment modelling | Create Equipment in Aveva e3d part 1 | Easy Method (2020)", "description": "Learn Aveva E3d equipment modelling by easy method. We give more helpful videos for our relations on Aveva E3d,  Sp3d modelling, PDMS modelling and much more. Thank you\n\nDownload link for Column :- https://up-load.io/ds12ml7an6zc\n\n#avevae3dequipment #e3dequipmentmodelling", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/kpG3y1l3Vrw/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/kpG3y1l3Vrw/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/kpG3y1l3Vrw/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/kpG3y1l3Vrw/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/kpG3y1l3Vrw/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 0, "resourceId": {"kind": "youtube#video", "videoId": "kpG3y1l3Vrw"}, "videoOwnerChannelTitle": "<PERSON><PERSON><PERSON>", "videoOwnerChannelId": "UCldg4hAylsOoKTuFnEKPbFA"}}, {"kind": "youtube#playlistItem", "etag": "vtQD9VdrAxkfTDFiOdK2yyjClog", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC4yODlGNEE0NkRGMEEzMEQy", "snippet": {"publishedAt": "2021-11-18T18:51:28Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "How to make an Installer package for any software or files", "description": "How to make an Installer package for any software or files\nWindows Installer is a software component and application programming interface of Microsoft Windows used for the installation, maintenance, and removal of software.\n\n✦✦✦✦✦✦✦ Join TeleGram ✦✦✦✦✦✦✦\n✦✦✦   https://t.me/buzz2daytech    ✦✦✦\n\n\n#Howto #Installer #Windows #Iexpress #Installerpackage\nDescription:\n#Buzz2Day Tech  \n\n►► Join Facebook Group for your Queries: \nhttps://www.facebook.com/groups/b2dtech/\n\nSocial Media:\nTelegram ► https://t.me/buzz2daytech\nSubscribe ► https://goo.gl/YWVPKH\nFacebook ► https://goo.gl/P4pq8q\nTwitter ►  https://goo.gl/vl7Nj5\nInstagram ► https://www.instagram.com/buzz2daytech/", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/t-ZiZcYl_GY/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/t-ZiZcYl_GY/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/t-ZiZcYl_GY/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/t-ZiZcYl_GY/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/t-ZiZcYl_GY/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 1, "resourceId": {"kind": "youtube#video", "videoId": "t-ZiZcYl_GY"}, "videoOwnerChannelTitle": "Buzz2day Tech", "videoOwnerChannelId": "UCSaA50dPTKM4XWZmZos8bpQ"}}, {"kind": "youtube#playlistItem", "etag": "ejglgEu-eCSerb2W7gq5UEX4hHA", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC4wMTcyMDhGQUE4NTIzM0Y5", "snippet": {"publishedAt": "2021-11-18T22:18:32Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "AVEVA E3D  Modeling Steel Platform", "description": "This is a detail video on creating the steel platform with stairs and railings. We used only the tools provided in the software to create the platform. We just show some of the technique you can use to do this. You can use some of the technique that we use. And we will be pleased to hear your comments.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/qr3prOkcV1M/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/qr3prOkcV1M/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/qr3prOkcV1M/hqdefault.jpg", "width": 480, "height": 360}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 2, "resourceId": {"kind": "youtube#video", "videoId": "qr3prOkcV1M"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "hzi3L9XdlU5uK6ggdAbhvWizxp4", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC41MjE1MkI0OTQ2QzJGNzNG", "snippet": {"publishedAt": "2021-11-18T22:18:46Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "AVEVA E3D - Curve Platform", "description": "AVEVA E3D \"How To\" create curve platform around a column with railings and ladders.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/nJicOcSWoRo/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/nJicOcSWoRo/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/nJicOcSWoRo/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/nJicOcSWoRo/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/nJicOcSWoRo/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 3, "resourceId": {"kind": "youtube#video", "videoId": "nJicOcSWoRo"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "MDqW6tVml5iCHbD88HbYuZKE9oQ", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC4wOTA3OTZBNzVEMTUzOTMy", "snippet": {"publishedAt": "2021-11-18T22:27:05Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "AVEVA Everything 3D 2.1", "description": "Ключевые возможности технологии AVEVA E3D 2.1 и наиболее полезные инструменты, встроенные в систему.\n\nДополнительная информация по решению AVEVA E3D Design\nhttps://www.aveva.com/ru-ru/products/e3d-design/", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/_orlNpZxnyY/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/_orlNpZxnyY/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/_orlNpZxnyY/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/_orlNpZxnyY/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/_orlNpZxnyY/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 4, "resourceId": {"kind": "youtube#video", "videoId": "_orlNpZxnyY"}, "videoOwnerChannelTitle": "AVEVA RUSSIA&CIS", "videoOwnerChannelId": "UCaRqW35w_E0pOeySvyjjUmw"}}, {"kind": "youtube#playlistItem", "etag": "xMM3Upk2s8LdP1W4lHEQJ158kZE", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC4xMkVGQjNCMUM1N0RFNEUx", "snippet": {"publishedAt": "2021-11-18T22:27:51Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "AVEVA E3D -  Pipe Modelling", "description": "Pipe  modelling in E3D in more details. We always welcome your comments and suggestions on anything you like us to share.\n\nFor services on implementation of AVEVA E3D on projects of any size, please contact <NAME_EMAIL>.  With  more than 100 years of design, modelling and development using AVEVA 3D Plant design software between our team, you will be satisfied with what we can do. We have support EPC worth more than US 1.5 billion in the last 18 months.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/BqJc-P3y-eI/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/BqJc-P3y-eI/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/BqJc-P3y-eI/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/BqJc-P3y-eI/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/BqJc-P3y-eI/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 5, "resourceId": {"kind": "youtube#video", "videoId": "BqJc-P3y-eI"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "nXc9AC0AU6BUAMgXZuOVhe7rT2c", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC41MzJCQjBCNDIyRkJDN0VD", "snippet": {"publishedAt": "2021-11-18T22:39:52Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "aveva e3d std equipment HE", "description": "", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/ltvETPgmXxE/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/ltvETPgmXxE/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/ltvETPgmXxE/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/ltvETPgmXxE/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/ltvETPgmXxE/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 6, "resourceId": {"kind": "youtube#video", "videoId": "ltvETPgmXxE"}, "videoOwnerChannelTitle": "<PERSON><PERSON><PERSON> (Senior Piping Designer)", "videoOwnerChannelId": "UCWxYrYKEIuGc7CuGV3Sl1hw"}}, {"kind": "youtube#playlistItem", "etag": "_7fq1eYJPKzormzyN8Eweq-U7kw", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC5DQUNERDQ2NkIzRUQxNTY1", "snippet": {"publishedAt": "2021-11-20T21:32:25Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "PIPE MODELING IN E3D", "description": "", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/kO-EdpEVrJ8/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/kO-EdpEVrJ8/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/kO-EdpEVrJ8/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/kO-EdpEVrJ8/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/kO-EdpEVrJ8/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 7, "resourceId": {"kind": "youtube#video", "videoId": "kO-EdpEVrJ8"}, "videoOwnerChannelTitle": "ATHER DALVI", "videoOwnerChannelId": "UCn8dmMWx18PQ42b14ruLkvA"}}, {"kind": "youtube#playlistItem", "etag": "_UOeX6H8MHfVGTG_vLA9h2nEK1A", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC45NDk1REZENzhEMzU5MDQz", "snippet": {"publishedAt": "2021-11-22T15:08:44Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "How to Fix Sound or Audio Problems on Windows 10", "description": "In this video I am going to show you How to Fix Sound or Audio Problems on Windows 10. Windows 10: How to fix sound issues after upgrade. How to fix audio sound problem not working on windows 10\nHow to fix sound drivers issues on Windows 10. Fix Realtek High Definition Audio Driver Issue for Windows 10. Sound problems in Windows 10? Here are some workaround. How to Fix Windows 10 Sound Issues. Fix windows 10 audio problems. how to fix audio problems in windows 7. how to fix audio problems in windows 8.windows 10 realtek audio problems. how to fix windows 8.1 problems.windows 10 no sound fix. windows 10 audio stutter. windows 10 audio driver. windows 10 sound problem\n\n★★★Top Online Courses From ProgrammingKnowledge ★★★\nPython Programming Course ➡️ http://bit.ly/2vsuMaS ⚫️ http://bit.ly/2GOaeQB\nJava Programming Course ➡️ http://bit.ly/2GEfQMf ⚫️ http://bit.ly/2Vvjy4a\nBash Shell Scripting Course ➡️ http://bit.ly/2DBVF0C ⚫️ http://bit.ly/2UM06vF\nLinux Command Line Tutorials ➡️ http://bit.ly/2IXuil0 ⚫️ http://bit.ly/2IXukt8\nC Programming Course ➡️ http://bit.ly/2GQCiD1 ⚫️ http://bit.ly/2ZGN6ej\nC++ Programming Course ➡️ http://bit.ly/2V4oEVJ ⚫️ http://bit.ly/2XMvqMs\nPHP Programming Course ➡️ http://bit.ly/2XP71WH ⚫️ http://bit.ly/2vs3od6\nAndroid Development Course ➡️ http://bit.ly/2UHih5H ⚫️ http://bit.ly/2IMhVci\nC# Programming Course ➡️ http://bit.ly/2Vr7HEl ⚫️ http://bit.ly/2W6RXTU\nJavaFx Programming Course ➡️ http://bit.ly/2XMvZWA ⚫️ http://bit.ly/2V2CoAi\nNodeJs Programming Course ➡️ http://bit.ly/2GPg7gA ⚫️ http://bit.ly/2GQYTQ2\nJenkins Course For Developers and DevOps ➡️ http://bit.ly/2Wd4l4W ⚫️ http://bit.ly/2J1B1ug\nScala Programming Tutorial Course ➡️ http://bit.ly/2PysyA4 ⚫️ http://bit.ly/2PCaVj2\nBootstrap Responsive Web Design Tutorial ➡️ http://bit.ly/2DFQ2yC ⚫️ http://bit.ly/2VoJWwH\nMongoDB Tutorial Course ➡️ http://bit.ly/2LaCJfP ⚫️ http://bit.ly/2WaI7Ap\nQT C++ GUI Tutorial For Beginners ➡️ http://bit.ly/2vwqHSZ\n\n★★★ Online Courses to learn ★★★\nGet 2 FREE Months of Unlimited Classes from skillshare - https://skillshare.eqcm.net/r1KEj\nData Science - http://bit.ly/2lD9h5L | http://bit.ly/2lI8wIl\nMachine Learning - http://bit.ly/2WGGQpb | http://bit.ly/2GghLXX  \nArtificial Intelligence - http://bit.ly/2lYqaYx | http://bit.ly/2NmaPya\nMERN Stack E-Degree Program - http://bit.ly/2kx2NFe | http://bit.ly/2lWj4no\nDevOps E-degree - http://bit.ly/2k1PwUQ | http://bit.ly/2k8Ypfy\nData Analytics with R - http://bit.ly/2lBKqz8 | http://bit.ly/2lAjos3\nAWS Certification Training - http://bit.ly/2kmLtTu | http://bit.ly/2lAkQL1\nProjects in Java - http://bit.ly/2kzn25d | http://bit.ly/2lBMffs\nMachine Learning With TensorFlow - http://bit.ly/2m1z3AF | http://bit.ly/2lBMhnA\nAngular 8 - Complete Essential Guide - http://bit.ly/2lYvYRP\nKotlin Android Development Masterclass - http://bit.ly/2GcblsI\nLearn iOS Programming Building Advance Projects - http://bit.ly/2kyX7ue\n\n\n★★★ Follow ★★★\nMy Website - http://www.codebind.com\n\nDISCLAIMER: This video and description contains affiliate links, which means that if you click on one of the product links, I’ll receive a small commission. This help support the channel and allows us to continue to make videos like this. Thank you for the support!", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/ncO8vekrfao/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/ncO8vekrfao/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/ncO8vekrfao/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/ncO8vekrfao/sddefault.jpg", "width": 640, "height": 480}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 8, "resourceId": {"kind": "youtube#video", "videoId": "ncO8vekrfao"}, "videoOwnerChannelTitle": "ProgrammingKnowledge2", "videoOwnerChannelId": "UC8aFE06Cti9OnQcKpl6rDvQ"}}, {"kind": "youtube#playlistItem", "etag": "srklrqtA4GFjHMAubO6TeZkVITE", "id": "UEwxY2pBR3lXR2xnbG81VmcxMDItemUya0F5VzItTGNrOC5GNjNDRDREMDQxOThCMDQ2", "snippet": {"publishedAt": "2021-11-24T10:05:35Z", "channelId": "UC0LZqtdqJTnp7dCNKhq9nzg", "title": "PP&P Engineering Process v2", "description": "CATIA - P&ID with Structure Design", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/2O6ExKA6Lgc/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/2O6ExKA6Lgc/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/2O6ExKA6Lgc/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/2O6ExKA6Lgc/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/2O6ExKA6Lgc/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "<PERSON><PERSON> dange", "playlistId": "PL1cjAGyWGlglo5Vg102-ze2kAyW2-Lck8", "position": 9, "resourceId": {"kind": "youtube#video", "videoId": "2O6ExKA6Lgc"}, "videoOwnerChannelTitle": "<PERSON>", "videoOwnerChannelId": "UCq22oiCWmclb68-11zxrchg"}}], "pageInfo": {"totalResults": 12, "resultsPerPage": 10}}}