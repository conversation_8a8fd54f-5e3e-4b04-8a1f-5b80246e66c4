{"version": "2.0.0", "tasks": [{"label": "Validate Current Feature", "type": "shell", "command": "./tools/validate_features.ps1", "args": ["-<PERSON><PERSON><PERSON>", "${file}"], "group": {"kind": "test", "isDefault": true}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Validate All Features", "type": "shell", "command": "./tools/validate_features.ps1", "args": ["-ValidateAll"], "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run Validator Tests", "type": "shell", "command": "python", "args": ["-m", "unittest", "tools/test_validator.py"], "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}]}