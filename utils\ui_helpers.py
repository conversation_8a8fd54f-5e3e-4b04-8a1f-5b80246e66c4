"""
Utility functions for symbol handling, tooltips, and error management
"""
import tkinter as tk
from tkinter import ttk
import logging
from typing import Dict, Optional, Callable

class SymbolManager:
    """Manages Unicode symbols with fallbacks"""
    
    SYMBOLS = {
        'bookmark': {
            'unicode': "★",
            'fallback': "*",
            'code': "U+2605"
        },
        'unbookmark': {
            'unicode': "☆",
            'fallback': "o",
            'code': "U+2606"
        },
        'delete': {
            'unicode': "🗑️",
            'fallback': "X",
            'code': "U+1F5D1"
        }
    }
    
    def get_symbol(self, name: str, fallback: Optional[str] = None) -> str:
        """
        Retrieves a symbol by name. If the Unicode symbol is not displayable,
        it falls back to a predefined ASCII character or a custom fallback.
        
        Args:
            name: The name of the symbol (e.g., 'bookmark', 'delete').
            fallback: An optional custom fallback character if the symbol is not found or displayable.
            
        Returns:
            The Unicode symbol if displayable, otherwise its fallback or the custom fallback.
        """
        symbol_info = self.SYMBOLS.get(name)
        if not symbol_info:
            return fallback if fallback is not None else '?' # Default fallback for unknown symbols

        unicode_char = symbol_info['unicode']
        default_fallback = symbol_info['fallback']

        # In a real GUI, you'd test if the character is displayable.
        # For this utility, we'll assume it's displayable unless explicitly told otherwise
        # or if a more robust displayability check is implemented.
        # For now, we'll just return the unicode char, or its default fallback.
        
        # A more robust check would involve trying to render it in a hidden Tkinter widget
        # and checking if its width/height is non-zero, but that's complex for a utility.
        
        return unicode_char if self._is_displayable(unicode_char) else (fallback if fallback is not None else default_fallback)

    def _is_displayable(self, symbol: str) -> bool:
        """
        Checks if a given Unicode symbol can be properly displayed by Tkinter.
        This is done by attempting to render the symbol in a hidden Tkinter widget
        and checking if it has a non-zero width and height.
        """
        root = None
        try:
            root = tk.Tk()
            root.withdraw()  # Hide the main window
            
            # Create a temporary label to test symbol rendering
            label = tk.Label(root, text=symbol, font=("TkDefaultFont", 12))
            label.pack()
            root.update_idletasks()  # Force update to get dimensions
            
            # Check if the label has a measurable size
            is_displayable = label.winfo_width() > 0 and label.winfo_height() > 0
            
            label.destroy()
            return is_displayable
        except tk.TclError as e:
            # This might happen if Tkinter cannot handle the symbol at all
            logging.warning(f"Tkinter TclError when checking symbol '{symbol}': {e}")
            return False
        except Exception as e:
            logging.error(f"Unexpected error checking symbol '{symbol}': {e}")
            return False
        finally:
            if root:
                root.destroy() # Ensure the temporary root is destroyed

class TooltipManager:
    """Manages tooltips with consistent timing and positioning"""
    
    def __init__(self, delay: int = 300):
        """
        Args:
            delay: Tooltip display delay in milliseconds
        """
        self.delay = delay
        self._tooltip = None
        self._timer = None
    
    def add_tooltip(self, widget: tk.Widget, text: str,
                   position: Optional[Callable] = None):
        """
        Add a tooltip to a widget
        
        Args:
            widget: Widget to add tooltip to
            text: Tooltip text
            position: Optional function to position tooltip
        """
        def show(event):
            if self._timer:
                widget.after_cancel(self._timer)
            
            def display():
                if self._tooltip:
                    self._tooltip.destroy()
                
                x = y = 0
                if position:
                    x, y = position(event)
                else:
                    x = widget.winfo_rootx() + 25
                    y = widget.winfo_rooty() + 20
                
                self.tooltip_window = tk.Toplevel(widget) # Parent the Toplevel to the widget
                self.tooltip_window.wm_overrideredirect(True)
                self.tooltip_window.wm_geometry(f"+{x}+{y}")
                
                label = tk.Label(self.tooltip_window, text=text,
                                justify=tk.LEFT,
                                background="#ffffe0",
                                relief=tk.SOLID,
                                borderwidth=1)
                label.pack()
            
            self._timer = widget.after(self.delay, display)
        
        def hide(event):
            if self._timer:
                widget.after_cancel(self._timer)
            if hasattr(self, 'tooltip_window') and self.tooltip_window: # Check if attribute exists and is not None
                self.tooltip_window.destroy()
                self.tooltip_window = None
        
        widget.bind('<Enter>', show)
        widget.bind('<Leave>', hide)

class ErrorManager:
    """Centralized error handling and user feedback"""
    
    ERROR_MESSAGES = {
        'api_limit': "YouTube API limit reached. Please try again later.",
        'network': "Unable to connect. Check your internet connection.",
        'symbols': "Unable to display symbols. Using simple alternatives.",
        'data': "Error loading bookmarks. Data may be corrupted."
    }
    
    def __init__(self, parent: tk.Widget):
        self.parent = parent
        self.logger = logging.getLogger(__name__)
    
    def show_error(self, error_type: str, message: str, details: str = ""):
        """
        Show error message to user and log it.
        
        Args:
            error_type: A string representing the type of error (e.g., 'api', 'data', 'ui').
            message: A concise message describing the error.
            details: Optional detailed error message for logging and extended display.
        """
        full_message = f"{message}\n\n{details}" if details else message
        self.logger.error(f"Error Type: {error_type}, Message: {message}, Details: {details}")
        tk.messagebox.showerror(f"{error_type.capitalize()} Error", full_message)

    def show_warning(self, warning_type: str, message: str, details: str = ""):
        """
        Show warning message to user and log it.
        
        Args:
            warning_type: A string representing the type of warning.
            message: A concise message describing the warning.
            details: Optional detailed warning message.
        """
        full_message = f"{message}\n\n{details}" if details else message
        self.logger.warning(f"Warning Type: {warning_type}, Message: {message}, Details: {details}")
        tk.messagebox.showwarning(f"{warning_type.capitalize()} Warning", full_message)

    def show_info(self, info_type: str, message: str, details: str = ""):
        """
        Show informational message to user and log it.
        
        Args:
            info_type: A string representing the type of information.
            message: A concise message describing the information.
            details: Optional detailed information message.
        """
        full_message = f"{message}\n\n{details}" if details else message
        self.logger.info(f"Info Type: {info_type}, Message: {message}, Details: {details}")
        tk.messagebox.showinfo(f"{info_type.capitalize()} Info", full_message)

class AnimationManager:
    """Manages UI animations and visual feedback."""

    def __init__(self, master: tk.Tk, logger: logging.Logger):
        self.master = master
        self.logger = logger

    def flash_widget(self, treeview: ttk.Treeview, item_id: str, color: str, duration: int):
        """
        Flashes a treeview item with a given color for a specified duration.
        """
        original_color = treeview.item(item_id, "tag")
        treeview.item(item_id, tags=(original_color, "flash"))
        treeview.tag_configure("flash", background=color)

        def reset_color():
            treeview.item(item_id, tags=(original_color,))
            treeview.tag_configure("flash", background="")

        self.master.after(duration, reset_color)
