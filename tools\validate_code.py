"""Validate code changes before applying updates."""

from pathlib import Path
from code_validator import ValidationManager

def main():
    """Run validation checks on project files."""
    project_root = Path(__file__).parent.parent
    validator = ValidationManager(project_root)
    
    # Files to validate
    files_to_check = [
        project_root / "gui" / "app.py",
        project_root / "logic" / "youtube_api.py",
        project_root / "data" / "bookmark_manager.py",
        project_root / "utils" / "ui_helpers.py"
    ]
    
    print("Running code validation checks...")
    
    # Validate files
    file_issues = validator.validate_update([str(f) for f in files_to_check])
    
    if file_issues:
        print("\nValidation issues found:")
        for file_path, issues in file_issues.items():
            print(f"\n{file_path}:")
            for issue in issues:
                print(f"  - {issue}")
    else:
        print("\nNo validation issues found!")
        
    # Return status code
    return 1 if file_issues else 0

if __name__ == "__main__":
    exit(main())
