{"timestamp": "2025-06-04T19:03:13.838806", "response": {"kind": "youtube#playlistItemListResponse", "etag": "3OIwCXRwNsnCyQIdPmfWQPYAQC4", "nextPageToken": "EAAaHlBUOkNBb2lFRVF3UVRCRlJqa3pSRU5GTlRjME1rSQ", "items": [{"kind": "youtube#playlistItem", "etag": "miFZOpjQRNCiS7F8Yy9HJUZeUp4", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi41NkI0NEY2RDEwNTU3Q0M2", "snippet": {"publishedAt": "2021-06-24T08:07:21Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D 3.1 - Reporting with Search", "description": "Our new \"How To\" series using E3D 3.1 for experienced E3D users. \nThis is the same \"How To\" video in E3D 2.1 but with the E3D 3.1 interface. We will show and explain how we create a simple report using the search feature.\n\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/qcb-RQUsKHM/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/qcb-RQUsKHM/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/qcb-RQUsKHM/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/qcb-RQUsKHM/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/qcb-RQUsKHM/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 0, "resourceId": {"kind": "youtube#video", "videoId": "qcb-RQUsKHM"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "LP9dD-zkl9zwElBnu0iqJhkWUrk", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi4yODlGNEE0NkRGMEEzMEQy", "snippet": {"publishedAt": "2021-11-30T12:56:35Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "Admin Tip for E3D - minor details count, the order of databases in a MDB", "description": "Our new tips using E3D 2.1 for experienced E3D users. \nIn this video we try to help our users be more efficient by creating using the order of the MDB to help them navigate easier. \n\nWe look at some Admin commands which are helpful in manipulating the MDB. Good administrators uses a lot of commands to automate a lot of administration work.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/_sZhDBcIRXA/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/_sZhDBcIRXA/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/_sZhDBcIRXA/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/_sZhDBcIRXA/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/_sZhDBcIRXA/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 1, "resourceId": {"kind": "youtube#video", "videoId": "_sZhDBcIRXA"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "JHbLR_QzHxxuhfUIubrPgoNCxRo", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi4wMTcyMDhGQUE4NTIzM0Y5", "snippet": {"publishedAt": "2022-01-17T01:57:28Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started : In-canvas Editor ( Learning Path 04 )", "description": "In this tutorial we look at the In-canvas Editor. This is a feature of E3D not found in PDMS. It is used in primitive creation and various manipulation in the 3D view. We have created a new playlist \"E3D for PDMS users\" and added E3D feature into this playlist to help users of PDMS.\n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 2, "resourceId": {"kind": "youtube#video", "videoId": "GHC04mLekhE"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "eQgJYmUx8ilRFJvfKVNUbMWwPNw", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi41MjE1MkI0OTQ2QzJGNzNG", "snippet": {"publishedAt": "2022-01-21T03:19:33Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started  - Using Event Driven Graphics EDG ( ( Learning Path 00 )", "description": "Event Driven Graphics or EDG was how we interact with the 3D canvas before the In-canvas Editor. It is a powerful tool and using it will make the modeling work more efficient. Even in E3D 3.1 , EDG is still being used. So your investment in learning EDG will not be wasted as you will be making use of it for years before it is being totally replaced. \n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 3, "resourceId": {"kind": "youtube#video", "videoId": "7BqHEEoGqkU"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "d6zbOGkYwro717W5HdqtQ5mbVOw", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi4wOTA3OTZBNzVEMTUzOTMy", "snippet": {"publishedAt": "2022-01-28T07:58:08Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started - Local coordinate system and In-canvas commands (  learning Path 00 )", "description": "We look at more E3D feature. The local coordinate system and how to use them as well as the highly convenient in-canvas command. \n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 4, "resourceId": {"kind": "youtube#video", "videoId": "toRQH-5P8VQ"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "1r-QWrgw-CQgI4LjI7uy3rL27LI", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi4xMkVGQjNCMUM1N0RFNEUx", "snippet": {"publishedAt": "2022-01-31T05:10:22Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D HowTo - Panel Panetrations", "description": "Let's look at how to create panetrations in Panels. I share with you some tips on doing more than what your trainer might have taught you. \n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/xoz3vRk6Azw/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/xoz3vRk6Azw/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/xoz3vRk6Azw/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/xoz3vRk6Azw/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/xoz3vRk6Azw/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 5, "resourceId": {"kind": "youtube#video", "videoId": "xoz3vRk6Azw"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "gjtJ1qbREwB3ClXm4gbyMDyfFz4", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi41MzJCQjBCNDIyRkJDN0VD", "snippet": {"publishedAt": "2022-02-24T03:29:27Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started Structural - Grid ( 02-01)", "description": "Let's get started with Structural Modelling in E3D. We wll get you started by showing the steps to create a structural frame starting with the grid up to the final structure. Hope this simple lesson will get you to begin modelling  structure in E3D. \n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 6, "resourceId": {"kind": "youtube#video", "videoId": "I42rv45HiGs"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "bzYBjRE_cXEwUnj9bfFtGcOEEGc", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi5DQUNERDQ2NkIzRUQxNTY1", "snippet": {"publishedAt": "2022-02-28T07:15:28Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started Structural - Portal Frame ( 02-02)", "description": "Continuing from where we stop, we will create a portal frame. In this video you can watch for the technique we use to create the frame. In this way, it will help you to choose the best method to model the structure.  After this we will do the last video to complate the model.\n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 7, "resourceId": {"kind": "youtube#video", "videoId": "VDUmev1eTMU"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "BTfALvO7GeFtLlijJgPzVw-GkgQ", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi5GNjNDRDREMDQxOThCMDQ2", "snippet": {"publishedAt": "2022-03-09T07:06:22Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started Structural - Completing the Structure ( 02-03)", "description": "We will introduce a few feature in E3D structure to complete our structure.\n\nWe will be using the :\na. Offset/Distance - to set a distance from the pline that we create. \nb. Local co-ordinate - to model purlin along the roof.\n\nHope this 3 videos get you started and explore the possibility with E3D Structure modelling. \n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 8, "resourceId": {"kind": "youtube#video", "videoId": "gJzoA-o3hL0"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "Vsfibt6dsJHo1luSHvmXM8YNQwk", "id": "UExqQ2hrb2dfcF9MRldYbmFzQkszOV9hejJYTXh0NlhrTi40NzZCMERDMjVEN0RFRThB", "snippet": {"publishedAt": "2022-04-05T10:47:57Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "AVEVA E3D - \"How To \" use Group ( GPWL, GPSET, GPITEM ) in E3D", "description": "Group is perhaps a gem in PDMS and now E3D .  It may have come from Unix Symbolic links. But it has been arround for a long time.  It is a really useful tool . You can use group :\na. to give you a different Breakdown Structure\nb. Phases of the project\nc. Favourites or my data. \nd. ....\n\nCheck out Group and the new user interface. Start using Group today to help you save time. We have a lot more topics to discuss and share. Please suggest topics you like. \n\nNow to keep us going doing this video, please use our services. \n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/ECfL7NSICfU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/ECfL7NSICfU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/ECfL7NSICfU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/ECfL7NSICfU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/ECfL7NSICfU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LFWXnasBK39_az2XMxt6XkN", "position": 9, "resourceId": {"kind": "youtube#video", "videoId": "ECfL7NSICfU"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}], "pageInfo": {"totalResults": 19, "resultsPerPage": 10}}}