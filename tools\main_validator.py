import argparse
import os
from pathlib import Path
from typing import List, Dict

# Assuming these are the main validator classes
from tools.copilot_validator import CopilotRulesValidator
from tools.code_validator import ValidationManager as CodeValidationManager
# from tools.update_validator import UpdateManager as UpdateValidationManager # To be integrated later

def run_copilot_validation(project_root: Path, feature_path: str = None) -> Dict[str, List[str]]:
    """Runs Copilot rules validation."""
    print(f"Running Copilot rules validation for: {feature_path if feature_path else 'all Python files'}")
    validator = CopilotRulesValidator(str(project_root))
    validations = []
    issues = {}

    if feature_path:
        try:
            validation = validator.validate_feature(feature_path)
            validations.append(validation)
        except Exception as e:
            issues[feature_path] = [f"Error validating: {e}"]
    else:
        for root, _, files in os.walk(project_root):
            for file in files:
                if file.endswith(".py"):
                    path = os.path.join(root, file)
                    try:
                        validation = validator.validate_feature(path)
                        validations.append(validation)
                    except Exception as e:
                        issues[path] = [f"Error validating: {e}"]
    
    if validations:
        report = validator.generate_report(validations)
        report_path = project_root / "copilot_validation_report.md"
        with open(report_path, "w") as f:
            f.write(report)
        print(f"Copilot validation report generated at: {report_path}")

    return issues

def run_code_validation(project_root: Path, files_to_check: List[str] = None) -> Dict[str, List[str]]:
    """Runs general code quality validation."""
    print(f"Running general code quality validation for: {files_to_check if files_to_check else 'default files'}")
    validator = CodeValidationManager(project_root)
    
    if not files_to_check:
        files_to_check = [
            project_root / "gui" / "app.py",
            project_root / "logic" / "youtube_api.py",
            project_root / "data" / "bookmark_manager.py",
            project_root / "utils" / "ui_helpers.py"
        ]
        files_to_check = [str(f) for f in files_to_check if f.exists()] # Ensure files exist

    file_issues = validator.validate_update(files_to_check)
    
    if file_issues:
        print("\nCode validation issues found:")
        for file_path, issues_list in file_issues.items():
            print(f"\n{file_path}:")
            for issue in issues_list:
                print(f"  - {issue}")
    else:
        print("\nNo code validation issues found!")
        
    return file_issues

def main():
    parser = argparse.ArgumentParser(description="Run various project validators.")
    parser.add_argument("--project-root", type=str, default=".", help="Root directory of the project.")
    parser.add_argument("--validate-all", action="store_true", help="Validate all Python files in the project.")
    parser.add_argument("--feature-path", type=str, help="Path to a specific feature file to validate.")
    parser.add_argument("--validate-code-quality", action="store_true", help="Run general code quality validation.")
    # parser.add_argument("--validate-updates", action="store_true", help="Run update validation (requires 'before' state).") # To be enabled later
    args = parser.parse_args()

    project_root = Path(args.project_root).resolve()
    total_issues = {}

    if args.validate_all or args.feature_path:
        copilot_issues = run_copilot_validation(project_root, args.feature_path)
        total_issues.update(copilot_issues)

    if args.validate_code_quality:
        code_issues = run_code_validation(project_root)
        total_issues.update(code_issues)

    # if args.validate_updates:
    #     # This would require a more complex setup to capture before/after states
    #     print("Update validation not yet fully implemented for this script.")

    if total_issues:
        print("\n--- Overall Validation Summary ---")
        print(f"Total issues found: {sum(len(v) for v in total_issues.values())}")
        exit(1)
    else:
        print("\nAll requested validations passed successfully!")
        exit(0)

if __name__ == "__main__":
    main()