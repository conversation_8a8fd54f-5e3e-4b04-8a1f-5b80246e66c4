# API Documentation - Documentation Reader

## 📚 Complete API Reference

This document provides comprehensive documentation for all classes, methods, and interfaces in the Documentation Reader application.

## 🖥️ Main Application API

### DocumentationReaderApp Class (`doc_reader_app.py`)

The main application window and controller for the Documentation Reader.

#### Constructor
```python
app = DocumentationReaderApp(docs_root_path: Path)
```
- **Parameters**: `docs_root_path` (Path) - Root directory containing documentation
- **Initializes**: UI components, file explorer, markdown renderer, and event connections

#### Core Methods

##### `init_ui() -> None`
Initialize the complete user interface.

**Functionality:**
- Creates main window layout
- Sets up header, content panels, and status bar
- Configures menu bar and toolbar
- Applies styling and themes

**Example:**
```python
app = DocumentationReaderApp(docs_root)
app.init_ui()  # Called automatically in constructor
```

##### `load_file(file_path: Path) -> None`
Load and display a documentation file.

**Parameters:**
- `file_path` (Path): Path to the file to load

**Functionality:**
- Validates file existence and permissions
- Determines file type and appropriate renderer
- Updates UI with file content
- Handles errors gracefully

**Example:**
```python
readme_path = Path("docs/README.md")
app.load_file(readme_path)
```

##### `on_file_selected(item: QTreeWidgetItem, column: int) -> None`
Handle file selection in the tree widget.

**Parameters:**
- `item` (QTreeWidgetItem): Selected tree item
- `column` (int): Column index (typically 0)

**Triggered by:** User clicking on file in tree view

##### `refresh_file_tree() -> None`
Refresh the file explorer tree.

**Functionality:**
- Rescans the documentation directory
- Updates tree widget with current files
- Preserves selection state when possible
- Updates status information

##### `open_external() -> None`
Open current file in external editor.

**Requirements:**
- File must be currently loaded
- System must have default application for file type

**Example:**
```python
# User presses Ctrl+E or double-clicks file
app.open_external()
```

##### `center_on_screen() -> None`
Center the application window on the screen.

**Usage:** Called during application startup for optimal positioning.

#### Event Handlers

##### `closeEvent(event: QCloseEvent) -> None`
Handle application close event with confirmation dialog.

**Parameters:**
- `event` (QCloseEvent): Qt close event object

**Behavior:**
- Shows confirmation dialog
- Accepts or ignores close event based on user choice

#### UI Creation Methods

##### `create_header(parent_layout: QVBoxLayout) -> None`
Create the application header with title and quick actions.

##### `create_main_content(parent_layout: QVBoxLayout) -> None`
Create the main content area with file explorer and preview panels.

##### `create_file_panel() -> QFrame`
Create the file explorer panel.

**Returns:** Configured QFrame containing file tree and information display

##### `create_content_panel() -> QFrame`
Create the content preview panel.

**Returns:** Configured QFrame containing markdown viewer

#### Status and Information Methods

##### `update_status() -> None`
Update status bar with current application information.

**Updates:**
- File count statistics
- Current file information
- Application status

##### `update_file_info(file_path: Path) -> None`
Update file information display.

**Parameters:**
- `file_path` (Path): File to analyze

**Displays:**
- File size and line count
- File type and modification date
- Additional metadata for markdown files

---

## 📄 Markdown Rendering API

### MarkdownRenderer Class (`markdown_renderer.py`)

Converts markdown content to styled HTML for display.

#### Constructor
```python
renderer = MarkdownRenderer()
```
- Initializes CSS styles and rendering configuration

#### Core Methods

##### `render_file(file_path: Path) -> str`
Render a markdown file to HTML.

**Parameters:**
- `file_path` (Path): Path to markdown file

**Returns:**
- `str`: Complete HTML document with styling

**Example:**
```python
renderer = MarkdownRenderer()
html_content = renderer.render_file(Path("README.md"))
```

##### `render_markdown(content: str) -> str`
Convert markdown content string to HTML.

**Parameters:**
- `content` (str): Raw markdown content

**Returns:**
- `str`: Styled HTML document

**Example:**
```python
markdown_text = "# Hello World\nThis is **bold** text."
html_output = renderer.render_markdown(markdown_text)
```

#### Processing Methods

##### `_process_lines(lines: List[str]) -> str`
Process markdown lines and convert to HTML.

**Parameters:**
- `lines` (List[str]): List of markdown lines

**Returns:**
- `str`: Processed HTML content

**Handles:**
- Headers (H1-H6)
- Code blocks with syntax highlighting
- Lists (ordered and unordered)
- Blockquotes
- Horizontal rules

##### `_process_inline(text: str) -> str`
Process inline markdown elements.

**Parameters:**
- `text` (str): Text containing inline markdown

**Returns:**
- `str`: HTML with processed inline elements

**Processes:**
- Bold and italic text
- Inline code
- Links
- HTML escaping

#### Utility Methods

##### `_escape_html(text: str) -> str`
Escape HTML characters for safe display.

##### `_get_css_styles() -> str`
Get CSS styles for rendered HTML.

**Returns:**
- `str`: Complete CSS stylesheet

**Includes:**
- Typography and fonts
- Color schemes
- Code block styling
- Responsive design rules

---

## 📁 File Explorer API

### FileExplorer Class (`file_explorer.py`)

Manages file system navigation and tree population.

#### Constructor
```python
explorer = FileExplorer(docs_root: Path)
```
- **Parameters**: `docs_root` (Path) - Root directory for exploration

#### Core Methods

##### `create_tree_widget() -> QTreeWidget`
Create and configure a tree widget for file display.

**Returns:**
- `QTreeWidget`: Configured tree widget

**Configuration:**
- Header labels and styling
- Font and appearance settings
- Interaction behaviors

##### `populate_tree(tree: QTreeWidget) -> None`
Populate tree widget with files and folders.

**Parameters:**
- `tree` (QTreeWidget): Tree widget to populate

**Functionality:**
- Recursive directory traversal
- File type detection and icon assignment
- Size information for markdown files
- Sorting and organization

##### `get_file_info(file_path: Path) -> dict`
Get detailed information about a file.

**Parameters:**
- `file_path` (Path): File to analyze

**Returns:**
- `dict`: File information dictionary

**Information Included:**
```python
{
    'name': str,           # File name
    'size': int,           # File size in bytes
    'modified': float,     # Modification timestamp
    'type': str,           # File extension
    'is_markdown': bool,   # Whether file is markdown
    'lines': int,          # Line count (for text files)
    'words': int,          # Word count (for markdown)
    'characters': int,     # Character count
    'headers': int         # Header count (for markdown)
}
```

#### Search and Statistics Methods

##### `search_files(query: str, file_type: str = 'all') -> list`
Search for files matching query.

**Parameters:**
- `query` (str): Search term
- `file_type` (str): File type filter ('all', 'md')

**Returns:**
- `list`: List of matching file paths

**Search Scope:**
- File names
- File content (for markdown files)

##### `get_documentation_stats() -> dict`
Get statistics about the documentation.

**Returns:**
- `dict`: Documentation statistics

**Statistics Included:**
```python
{
    'total_files': int,      # Total file count
    'markdown_files': int,   # Markdown file count
    'total_size': int,       # Total size in bytes
    'total_lines': int,      # Total line count
    'folders': int           # Folder count
}
```

##### `get_recent_files(limit: int = 5) -> list`
Get recently modified files.

**Parameters:**
- `limit` (int): Maximum number of files to return

**Returns:**
- `list`: List of recently modified file paths

#### Internal Methods

##### `_add_directory_contents(parent_item: QTreeWidgetItem, directory: Path) -> None`
Recursively add directory contents to tree.

##### `_get_file_icon(file_path: Path) -> str`
Get appropriate emoji icon for file type.

##### `_contains_markdown_files(directory: Path) -> bool`
Check if directory contains markdown files.

---

## 🎨 Theme and Styling API

### Theme System (`main.py`)

#### `apply_dark_theme(app: QApplication) -> None`
Apply dark theme styling to the application.

**Parameters:**
- `app` (QApplication): Application instance

**Styling Applied:**
- Window and widget backgrounds
- Text colors and selection colors
- Scrollbar styling
- Button and menu styling
- Tree widget and text editor styling

**Example:**
```python
app = QApplication(sys.argv)
apply_dark_theme(app)
```

---

## 🚀 Launcher API

### Smart Launcher (`launch.py`)

#### `check_dependencies() -> bool`
Check if required dependencies are installed.

**Returns:**
- `bool`: True if all dependencies available

#### `install_dependencies() -> bool`
Attempt to install missing dependencies.

**Returns:**
- `bool`: True if installation successful

#### `main() -> bool`
Main launcher function with dependency management.

**Returns:**
- `bool`: True if application launched successfully

**Functionality:**
- Python version checking
- Dependency validation
- Automatic installation option
- Error handling and user guidance

---

## 🔧 Configuration and Constants

### File Type Icons
```python
file_icons = {
    '.md': '📄',        # Markdown files
    '.txt': '📝',       # Text files
    '.py': '🐍',        # Python files
    '.json': '📋',      # JSON files
    '.yaml': '⚙️',      # YAML files
    '.yml': '⚙️',       # YAML files
    'folder': '📁',     # Directories
    'default': '📄'     # Default file type
}
```

### CSS Style Classes
```css
.header-1, .header-2, .header-3    /* Header styling */
.paragraph                         /* Paragraph styling */
.bold, .italic                     /* Text formatting */
.inline-code, .code-block          /* Code styling */
.unordered-list, .ordered-list     /* List styling */
.blockquote                        /* Quote styling */
.link                              /* Link styling */
.divider                           /* Horizontal rule */
```

### Keyboard Shortcuts
- **F5**: Refresh file tree
- **Ctrl+E**: Open in external editor
- **Ctrl+Q**: Exit application
- **Enter**: Select file in tree
- **Arrow Keys**: Navigate tree

---

## 🚨 Error Handling

### Exception Types
- **FileNotFoundError**: File doesn't exist
- **PermissionError**: Access denied
- **UnicodeDecodeError**: File encoding issues
- **Qt Exceptions**: UI-related errors

### Error Recovery
- **Graceful degradation**: Continue operation when possible
- **User notification**: Clear error messages
- **Logging**: Detailed error information for debugging
- **State preservation**: Maintain application state

---

## 📊 Performance Considerations

### Optimization Guidelines
- **File Loading**: Use efficient file reading methods
- **Tree Population**: Minimize recursive operations
- **HTML Rendering**: Cache processed content when possible
- **UI Updates**: Batch updates to prevent flickering

### Memory Management
- **Resource Cleanup**: Proper Qt object disposal
- **Content Limits**: Handle large files appropriately
- **Cache Management**: Prevent memory leaks

---

## 🔗 Integration Examples

### Custom Markdown Renderer
```python
# Extend the markdown renderer
class CustomMarkdownRenderer(MarkdownRenderer):
    def __init__(self):
        super().__init__()
        self.custom_css = self._get_custom_styles()

    def _get_custom_styles(self):
        return self.css_styles + """
        .custom-highlight {
            background-color: #fffacd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        """
```

### External Plugin Integration
```python
# Plugin interface example
class DocumentationPlugin:
    def __init__(self, app: DocumentationReaderApp):
        self.app = app

    def process_file(self, file_path: Path) -> str:
        # Custom file processing
        pass

    def add_menu_items(self, menu_bar):
        # Add plugin menu items
        pass
```

### File System Monitoring
```python
# Watch for file changes
from PyQt5.QtCore import QFileSystemWatcher

watcher = QFileSystemWatcher()
watcher.addPath(str(docs_root))
watcher.fileChanged.connect(app.refresh_file_tree)
```

---

*API Documentation v1.0 - June 17, 2025*
