#!/usr/bin/env python3
"""Feature validation script for the YouTube Explorer application."""

import sys
import os
import yaml
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeatureValidator:
    """Validates feature implementations against defined rules."""
    
    def __init__(self, rules_path: str):
        """Initialize validator with rules file path."""
        self.rules_path = Path(rules_path)
        self.rules = self._load_rules()
        
    def _load_rules(self) -> dict:
        """Load validation rules from the rules file."""
        try:
            with open(self.rules_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load rules: {e}")
            return {}
            
    def validate_feature(self, feature_path: str) -> bool:
        """Validate a single feature implementation."""
        try:
            with open(feature_path, 'r', encoding='utf-8') as f:
                feature = yaml.safe_load(f)
            return self._check_feature_compliance(feature)
        except Exception as e:
            logger.error(f"Failed to validate feature {feature_path}: {e}")
            return False
            
    def _check_feature_compliance(self, feature: dict) -> bool:
        """Check if feature complies with all rules."""
        required_fields = self.rules.get('Feature', {}).get('required_fields', [])
        
        # Check required fields
        for field in required_fields:
            if field not in feature:
                logger.error(f"Missing required field: {field}")
                return False
                
        # Validate technical requirements
        if not self._validate_technical_requirements(feature):
            return False
            
        # Validate documentation
        if not self._validate_documentation(feature):
            return False
            
        return True
        
    def _validate_technical_requirements(self, feature: dict) -> bool:
        """Validate technical requirements of a feature."""
        tech_reqs = feature.get('technical_requirements', {})
        if not tech_reqs:
            logger.error("Missing technical requirements")
            return False
            
        required_tech_fields = ['performance', 'dependencies', 'compatibility']
        for field in required_tech_fields:
            if field not in tech_reqs:
                logger.error(f"Missing technical requirement: {field}")
                return False
                
        return True
        
    def _validate_documentation(self, feature: dict) -> bool:
        """Validate feature documentation."""
        docs = feature.get('documentation', {})
        if not docs:
            logger.error("Missing documentation")
            return False
            
        required_doc_fields = ['usage', 'api', 'examples']
        for field in required_doc_fields:
            if field not in docs:
                logger.error(f"Missing documentation section: {field}")
                return False
                
        return True

def main():
    """Main validation function."""
    if len(sys.argv) < 2:
        logger.error("Please provide path to feature file")
        sys.exit(1)
        
    validator = FeatureValidator('../rules/validation_rules.md')
    feature_path = sys.argv[1]
    
    if validator.validate_feature(feature_path):
        logger.info(f"Feature validation successful: {feature_path}")
        sys.exit(0)
    else:
        logger.error(f"Feature validation failed: {feature_path}")
        sys.exit(1)

if __name__ == '__main__':
    main()
