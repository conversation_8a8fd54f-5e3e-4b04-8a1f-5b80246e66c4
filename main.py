# Main entry point for the YouTube Explorer Application

from gui.app import <PERSON><PERSON>x<PERSON>lorer<PERSON><PERSON>
from dotenv import load_dotenv
import os
from utils.logger import AppLogger

class MainApplicationRunner:
    """
    Main application runner to conform to FeatureImplementation structure for validation.
    This class acts as the entry point and orchestrates the YouTubeExplorerApp.
    """
    def __init__(self):
        self.logger = None
        self.initialize_logging()
        self.validate_requirements()
        self.setup_error_handling()
        self.logger.logger.info("MainApplicationRunner initialized.")

    def validate_requirements(self):
        """Validates essential requirements for the main application."""
        load_dotenv()
        if not os.getenv("API_KEY"):
            self.logger.logger.error("API_KEY environment variable not set in .env file.")
            raise ValueError("API_KEY is not set. Please configure your .env file.")
        self.logger.logger.info("Main application requirements validated.")

    def setup_error_handling(self):
        """Sets up error handling for the main application runner."""
        self.logger.logger.info("Main application error handling setup complete.")

    def initialize_logging(self):
        """Initializes logging for the main application runner."""
        self.logger = AppLogger()
        self.logger.logger.info("Main application logging initialized.")

    def run(self):
        """Runs the YouTube Explorer application."""
        self.logger.logger.info("Starting YouTube Explorer App from MainApplicationRunner.")
        app = YouTubeExplorerApp()
        app.run()

if __name__ == "__main__":
    runner = MainApplicationRunner()
    runner.run()
