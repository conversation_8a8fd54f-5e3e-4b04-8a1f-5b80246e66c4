name: Copilot Rules Validation

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  validate:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.13'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyyaml
        
    - name: Run validator tests
      run: |
        python -m unittest tools/test_validator.py
        
    - name: Validate all Python files
      run: |
        ./tools/validate_features.ps1 -ValidateAll
        
    - name: Generate validation report
      if: always()
      run: |
        python tools/copilot_validator.py > validation_report.md
        
    - name: Upload validation report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: validation-report
        path: validation_report.md
