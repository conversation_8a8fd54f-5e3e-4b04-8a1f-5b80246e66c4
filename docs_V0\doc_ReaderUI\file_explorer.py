"""
File explorer component for browsing documentation files
"""

import os
from pathlib import Path
from PyQt5.QtWidgets import QTreeWidget, QTreeWidgetItem
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont

class FileExplorer:
    """File explorer for documentation files"""

    def __init__(self, docs_root: Path):
        self.docs_root = docs_root
        self.file_icons = {
            '.md': '📄',
            '.txt': '📝',
            '.py': '🐍',
            '.json': '📋',
            '.yaml': '⚙️',
            '.yml': '⚙️',
            'folder': '📁',
            'default': '📄'
        }

    def create_tree_widget(self) -> QTreeWidget:
        """Create and configure the tree widget"""

        tree = QTreeWidget()
        tree.setHeaderLabel("Documentation Files")
        tree.setRootIsDecorated(True)
        tree.setAlternatingRowColors(False)
        tree.setAnimated(True)
        tree.setIndentation(20)

        # Set font
        font = QFont("Segoe UI", 15)
        tree.setFont(font)

        return tree

    def expand_all(self, tree_widget):
        """Expand all items in the tree"""
        tree_widget.expandAll()

    def collapse_all(self, tree_widget):
        """Collapse all items in the tree"""
        tree_widget.collapseAll()

    def set_font_size(self, tree_widget, font_size):
        """Set font size for the tree widget"""
        font = QFont("Segoe UI", font_size)
        tree_widget.setFont(font)

    def populate_tree(self, tree: QTreeWidget):
        """Populate the tree widget with files and folders"""

        tree.clear()

        # Create root item
        root_item = QTreeWidgetItem(tree)
        root_item.setText(0, f"📚 {self.docs_root.name}")
        root_item.setData(0, Qt.UserRole, str(self.docs_root))
        root_item.setExpanded(True)

        # Add files and folders
        self._add_directory_contents(root_item, self.docs_root)

        # Sort items
        tree.sortItems(0, Qt.AscendingOrder)

    def _add_directory_contents(self, parent_item: QTreeWidgetItem, directory: Path):
        """Recursively add directory contents to tree"""

        try:
            # Get all items in directory
            items = list(directory.iterdir())

            # Separate folders and files
            folders = [item for item in items if item.is_dir() and not item.name.startswith('.')]
            files = [item for item in items if item.is_file() and not item.name.startswith('.')]

            # Add folders first
            for folder in sorted(folders):
                folder_item = QTreeWidgetItem(parent_item)
                folder_item.setText(0, f"{self.file_icons['folder']} {folder.name}")
                folder_item.setData(0, Qt.UserRole, str(folder))

                # Recursively add folder contents
                self._add_directory_contents(folder_item, folder)

                # Expand if it contains markdown files
                if self._contains_markdown_files(folder):
                    folder_item.setExpanded(True)

            # Add files
            for file in sorted(files):
                file_item = QTreeWidgetItem(parent_item)

                # Get appropriate icon
                icon = self._get_file_icon(file)

                # Set display text with file info
                display_text = f"{icon} {file.name}"

                # Add file size for markdown files
                if file.suffix.lower() == '.md':
                    size = file.stat().st_size
                    if size < 1024:
                        size_str = f"{size}B"
                    elif size < 1024 * 1024:
                        size_str = f"{size//1024}KB"
                    else:
                        size_str = f"{size//(1024*1024)}MB"

                    display_text += f" ({size_str})"

                file_item.setText(0, display_text)
                file_item.setData(0, Qt.UserRole, str(file))

                # Highlight markdown files
                if file.suffix.lower() == '.md':
                    font = file_item.font(0)
                    font.setBold(True)
                    file_item.setFont(0, font)

        except PermissionError:
            # Handle permission errors gracefully
            error_item = QTreeWidgetItem(parent_item)
            error_item.setText(0, "❌ Permission denied")
            error_item.setDisabled(True)

    def _get_file_icon(self, file_path: Path) -> str:
        """Get appropriate icon for file type"""

        extension = file_path.suffix.lower()
        return self.file_icons.get(extension, self.file_icons['default'])

    def _contains_markdown_files(self, directory: Path) -> bool:
        """Check if directory contains markdown files"""

        try:
            for item in directory.rglob('*.md'):
                return True
            return False
        except:
            return False

    def get_file_info(self, file_path: Path) -> dict:
        """Get detailed information about a file"""

        try:
            stat = file_path.stat()

            info = {
                'name': file_path.name,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'type': file_path.suffix.lower(),
                'is_markdown': file_path.suffix.lower() == '.md'
            }

            # For markdown files, get additional info
            if info['is_markdown']:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        info['lines'] = len(content.splitlines())
                        info['words'] = len(content.split())
                        info['characters'] = len(content)

                        # Count headers
                        headers = [line for line in content.splitlines() if line.strip().startswith('#')]
                        info['headers'] = len(headers)

                except:
                    info['lines'] = 0
                    info['words'] = 0
                    info['characters'] = 0
                    info['headers'] = 0

            return info

        except Exception as e:
            return {'error': str(e)}

    def search_files(self, query: str, file_type: str = 'all') -> list:
        """Search for files matching query"""

        results = []

        try:
            # Search in file names
            if file_type == 'all' or file_type == 'md':
                for md_file in self.docs_root.rglob('*.md'):
                    if query.lower() in md_file.name.lower():
                        results.append(md_file)

            # Search in content (for markdown files)
            if file_type == 'all' or file_type == 'md':
                for md_file in self.docs_root.rglob('*.md'):
                    try:
                        with open(md_file, 'r', encoding='utf-8') as f:
                            content = f.read().lower()
                            if query.lower() in content and md_file not in results:
                                results.append(md_file)
                    except:
                        continue

        except Exception as e:
            print(f"Search error: {e}")

        return results

    def get_documentation_stats(self) -> dict:
        """Get statistics about the documentation"""

        stats = {
            'total_files': 0,
            'markdown_files': 0,
            'total_size': 0,
            'total_lines': 0,
            'folders': 0
        }

        try:
            for item in self.docs_root.rglob('*'):
                if item.is_file():
                    stats['total_files'] += 1
                    stats['total_size'] += item.stat().st_size

                    if item.suffix.lower() == '.md':
                        stats['markdown_files'] += 1

                        try:
                            with open(item, 'r', encoding='utf-8') as f:
                                stats['total_lines'] += len(f.readlines())
                        except:
                            pass

                elif item.is_dir():
                    stats['folders'] += 1

        except Exception as e:
            stats['error'] = str(e)

        return stats

    def get_recent_files(self, limit: int = 5) -> list:
        """Get recently modified files"""

        try:
            files = []
            for md_file in self.docs_root.rglob('*.md'):
                stat = md_file.stat()
                files.append((md_file, stat.st_mtime))

            # Sort by modification time (newest first)
            files.sort(key=lambda x: x[1], reverse=True)

            return [file[0] for file in files[:limit]]

        except Exception as e:
            print(f"Error getting recent files: {e}")
            return []
