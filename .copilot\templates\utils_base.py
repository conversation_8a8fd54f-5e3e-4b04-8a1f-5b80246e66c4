"""Base utility class template for consistent implementation."""

import logging
from typing import Any, Dict, Optional
from abc import ABC, abstractmethod

class BaseUtility(ABC):
    """Abstract base class for utility implementations."""
    
    def __init__(self):
        """Initialize base utility."""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._initialize()
    
    @abstractmethod
    def _initialize(self):
        """Initialize utility-specific resources."""
        pass
    
    def handle_error(self, error_type: str, detail: str, context: Optional[Dict[str, Any]] = None) -> None:
        """Handle utility errors consistently.
        
        Args:
            error_type: Type of error from ErrorTypes
            detail: Detailed error message
            context: Optional context dictionary
        """
        log_entry = {
            'utility': self.__class__.__name__,
            'error_type': error_type,
            'detail': detail,
            'context': context or {}
        }
        self.logger.error(f"Utility error: {log_entry}")
        self._handle_specific_error(error_type, detail, context)
    
    @abstractmethod
    def _handle_specific_error(self, error_type: str, detail: str, context: Optional[Dict[str, Any]]) -> None:
        """Handle utility-specific error cases.
        
        Args:
            error_type: Type of error from ErrorTypes
            detail: Detailed error message
            context: Optional context dictionary
        """
        pass
    
    def cleanup(self) -> None:
        """Clean up utility resources."""
        try:
            self._cleanup_specific()
        except Exception as e:
            self.handle_error('cleanup_failed', str(e))
    
    @abstractmethod
    def _cleanup_specific(self) -> None:
        """Clean up utility-specific resources."""
        pass

class ExampleUtility(BaseUtility):
    """Example implementation of BaseUtility."""
    
    def _initialize(self):
        """Initialize example utility."""
        self.cache = {}
    
    def _handle_specific_error(self, error_type: str, detail: str, context: Optional[Dict[str, Any]]) -> None:
        """Handle example utility errors."""
        if error_type == 'cache_error':
            self.cache.clear()
    
    def _cleanup_specific(self) -> None:
        """Clean up example utility resources."""
        self.cache.clear()
