import unittest
import tkinter as tk
from tkinter import messagebox
from unittest.mock import patch, MagicMock
from utils.ui_helpers import SymbolManager, TooltipManager, ErrorManager

class TestUIHelpers(unittest.TestCase):
    """
    Feature Name: UI Helpers Unit Tests
        Unit tests for SymbolManager, TooltipManager, and ErrorManager classes,
        focusing on their individual functionalities and error handling.

    Requirements:
        - Test SymbolManager's symbol retrieval and fallback.
        - Test TooltipManager's tooltip display and hiding.
        - Test ErrorManager's error display and logging.

    Parameters:
        None

    Returns:
        None

    Raises:
        AssertionError: If any test assertion fails.

    Example:
        Run tests using: python -m unittest tests/unit/test_ui_helpers.py
    """

    def setUp(self):
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the main window

    def tearDown(self):
        if self.root:
            self.root.destroy()

    def test_symbol_manager_get_symbol_success(self):
        """Test SymbolManager retrieves existing symbols."""
        with patch('utils.ui_helpers.SymbolManager._is_displayable', return_value=True) as mock_is_displayable:
            sm = SymbolManager()
            self.assertEqual(sm.get_symbol('bookmark'), '★')
            self.assertEqual(sm.get_symbol('delete'), '🗑️')
            mock_is_displayable.assert_any_call('★')
            mock_is_displayable.assert_any_call('🗑️')

    def test_symbol_manager_get_symbol_fallback(self):
        """Test SymbolManager falls back to ASCII for missing symbols."""
        with patch('utils.ui_helpers.SymbolManager._is_displayable', return_value=False) as mock_is_displayable:
            sm = SymbolManager()
            self.assertEqual(sm.get_symbol('bookmark'), '*') # Expecting fallback for 'bookmark'
            self.assertEqual(sm.get_symbol('unbookmark'), 'o') # Expecting fallback for 'unbookmark'
            self.assertEqual(sm.get_symbol('non_existent_symbol'), '?')
            self.assertEqual(sm.get_symbol('non_existent_symbol', fallback='X'), 'X')
            mock_is_displayable.assert_any_call('★')
            mock_is_displayable.assert_any_call('☆')

    @patch('tkinter.Toplevel')
    @patch('tkinter.Label')
    def test_tooltip_manager_show_tooltip(self, MockLabel, MockToplevel):
        """Test TooltipManager displays a tooltip."""
        tm = TooltipManager()
        widget = MagicMock(spec=tk.Widget) # Mock the widget
        widget.winfo_rootx.return_value = 0
        widget.winfo_rooty.return_value = 0
        widget.after = MagicMock() # Mock the after method
        
        mock_toplevel = MagicMock()
        MockToplevel.return_value = mock_toplevel
        mock_label = MagicMock()
        MockLabel.return_value = mock_label

        tm.add_tooltip(widget, "Test Tooltip")
        
        # Get the callback function passed to widget.after
        widget.after.assert_called_once()
        args, _ = widget.after.call_args
        callback_function = args[1]
        
        # Execute the callback function directly
        callback_function()

        # Check if tooltip window is created and label is configured
        self.assertIsNotNone(tm.tooltip_window)
        mock_label.config.assert_called_with(text="Test Tooltip", justify=tk.LEFT, background="#ffffe0", relief=tk.SOLID, borderwidth=1)
        mock_label.pack.assert_called_once()
        mock_toplevel.wm_overrideredirect.assert_called_with(True)
        mock_toplevel.wm_geometry.assert_called_once()

    def test_tooltip_manager_hide_tooltip(self):
        """Test TooltipManager hides a tooltip."""
        tm = TooltipManager()
        widget = MagicMock(spec=tk.Widget) # Mock the widget
        widget.bind = MagicMock() # Mock the bind method
        
        # Manually set up a mock tooltip window for the hide test
        tm.tooltip_window = MagicMock()
        
        tm.add_tooltip(widget, "Test Tooltip") # This will bind the hide function
        
        # Get the callback function passed to widget.bind for '<Leave>'
        hide_callback = None
        for call_args, _ in widget.bind.call_args_list:
            if call_args[0] == '<Leave>':
                hide_callback = call_args[1]
                break
        
        self.assertIsNotNone(hide_callback, "Hide callback not bound")
        
        # Execute the hide callback directly
        hide_callback(None) # Pass None for event as it's not used in hide

        # Check if tooltip window is destroyed
        tm.tooltip_window.destroy.assert_called_once()
        self.assertIsNone(tm.tooltip_window)

    @patch('tkinter.messagebox.showerror')
    @patch('utils.logger.AppLogger')
    def test_error_manager_show_error(self, MockAppLogger, mock_showerror):
        """Test ErrorManager displays error message and logs."""
        mock_logger_instance = MockAppLogger.return_value.logger
        em = ErrorManager(self.root)
        em.logger = mock_logger_instance # Assign the mock logger

        em.show_error('api', 'API call failed', 'Detailed API error message.')
        
        mock_showerror.assert_called_once_with("Api Error", "API call failed\n\nDetailed API error message.")
        mock_logger_instance.error.assert_called_once_with("Error Type: api, Message: API call failed, Details: Detailed API error message.")

    @patch('tkinter.messagebox.showwarning')
    @patch('utils.logger.AppLogger')
    def test_error_manager_show_warning(self, MockAppLogger, mock_showwarning):
        """Test ErrorManager displays warning message and logs."""
        mock_logger_instance = MockAppLogger.return_value.logger
        em = ErrorManager(self.root)
        em.logger = mock_logger_instance

        em.show_warning('data', 'Data corruption detected', 'Cache needs rebuilding.')
        
        mock_showwarning.assert_called_once_with("Data Warning", "Data corruption detected\n\nCache needs rebuilding.")
        mock_logger_instance.warning.assert_called_once_with("Warning Type: data, Message: Data corruption detected, Details: Cache needs rebuilding.")

    @patch('tkinter.messagebox.showinfo')
    @patch('utils.logger.AppLogger')
    def test_error_manager_show_info(self, MockAppLogger, mock_showinfo):
        """Test ErrorManager displays info message and logs."""
        mock_logger_instance = MockAppLogger.return_value.logger
        em = ErrorManager(self.root)
        em.logger = mock_logger_instance

        em.show_info('system', 'Update available', 'Version 1.1 is ready.')
        
        mock_showinfo.assert_called_once_with("System Info", "Update available\n\nVersion 1.1 is ready.")
        mock_logger_instance.info.assert_called_once_with("Info Type: system, Message: Update available, Details: Version 1.1 is ready.")

if __name__ == '__main__':
    unittest.main()
