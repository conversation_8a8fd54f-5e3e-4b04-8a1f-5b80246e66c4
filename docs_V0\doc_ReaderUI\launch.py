#!/usr/bin/env python3
"""
Simple launcher script for the Documentation Reader
Handles dependency checking and provides user-friendly error messages
"""

import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    
    try:
        import PyQt5
        print("✅ PyQt5 found")
        return True
    except ImportError:
        print("❌ PyQt5 not found")
        return False

def install_dependencies():
    """Attempt to install missing dependencies"""
    
    print("\n🔧 Installing PyQt5...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt5>=5.15.0"])
        print("✅ PyQt5 installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install PyQt5")
        return False

def main():
    """Main launcher function"""
    
    print("🚀 YouTube Explorer Documentation Reader Launcher")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 6):
        print("❌ Python 3.6 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Check dependencies
    if not check_dependencies():
        print("\n📦 Missing dependencies detected")
        response = input("Would you like to install PyQt5 automatically? (y/n): ")
        
        if response.lower() in ['y', 'yes']:
            if not install_dependencies():
                print("\n❌ Installation failed. Please install PyQt5 manually:")
                print("   pip install PyQt5")
                return False
        else:
            print("\n📝 Please install PyQt5 manually:")
            print("   pip install PyQt5")
            return False
    
    # Launch the application
    print("\n🎯 Launching Documentation Reader...")
    try:
        # Import and run the main application
        from main import main as run_app
        run_app()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all application files are present")
        return False
    
    except Exception as e:
        print(f"❌ Application error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Troubleshooting tips:")
        print("   1. Ensure Python 3.6+ is installed")
        print("   2. Install PyQt5: pip install PyQt5")
        print("   3. Check that all application files are present")
        print("   4. Run from the doc_ReaderUI directory")
        
        input("\nPress Enter to exit...")
        sys.exit(1)
