---
noteId: "991706b1422d11f0a6380b892c29f86c"
tags: []

---

# Copilot Implementation Guidelines

## 1. Feature Specification Format

### 1.1 Base Template
```yaml
Feature:
  name: "Feature Name"
  description: "Concise description"
  technical_requirements:
    - Exact technical specifications
    - Required libraries/dependencies
    - Performance constraints
  visual_elements:
    symbols:
      - character: "Symbol" # Exact Unicode value
        fallback: "ASCII alternative"
        size: "dimensions in px"
    colors:
      - name: "Color name"
        value: "HEX/RGB value"
      - contrast_ratio: "minimum required"
  validation_criteria:
    - Specific testable requirements
    - Expected behaviors
    - Performance metrics
```

### 1.2 Implementation Checklist Template
```yaml
Implementation:
  preparation:
    □ Dependencies verified
    □ Environment validated
    □ Resources available
  core_features:
    □ Basic functionality
    □ Error handling
    □ Performance requirements
  testing:
    □ Unit tests
    □ Integration tests
    □ User acceptance criteria
  documentation:
    □ Code documentation
    □ User documentation
    □ API documentation
```

## 2. Required Components for Each Feature

### 2.1 Data Schema
```yaml
data_schema:
  required_fields:
    - name: "field_name"
      type: "data_type"
      constraints: ["list", "of", "constraints"]
  optional_fields:
    - name: "field_name"
      type: "data_type"
      default: "default_value"
  validation_rules:
    - rule: "validation rule"
      error_message: "user friendly message"
```

### 2.2 Error Handling Matrix
```yaml
error_handling:
  user_errors:
    - condition: "error condition"
      message: "user-friendly message"
      recovery: "recovery action"
  system_errors:
    - condition: "error condition"
      logging: "what to log"
      fallback: "fallback behavior"
  validation_errors:
    - type: "validation error"
      handling: "how to handle"
      prevention: "prevention strategy"
```

### 2.3 Testing Requirements
```yaml
testing:
  unit_tests:
    - feature: "feature name"
      test_cases:
        - scenario: "test scenario"
          inputs: ["test inputs"]
          expected: "expected output"
  integration_tests:
    - components: ["component1", "component2"]
      scenarios: ["test scenarios"]
  performance_tests:
    - metric: "metric name"
      threshold: "acceptable threshold"
```

## 3. Implementation Rules

### 3.1 Code Structure
```python
# Required structure for feature implementation
class FeatureImplementation:
    def __init__(self):
        # Required initialization components
        self.validate_requirements()
        self.setup_error_handling()
        self.initialize_logging()

    def validate_requirements(self):
        # Must check all dependencies and requirements
        pass

    def setup_error_handling(self):
        # Must implement comprehensive error handling
        pass

    def initialize_logging(self):
        # Must set up proper logging
        pass
```

### 3.2 Documentation Requirements
```python
def feature_implementation():
    """
    Required documentation format:
    
    Purpose:
        Clear description of feature purpose
    
    Requirements:
        - List of all requirements
        - Technical constraints
        - Dependencies
    
    Parameters:
        param1 (type): description
        param2 (type): description
    
    Returns:
        type: description
    
    Raises:
        ErrorType: error description
        
    Example:
        Concrete usage example
    """
    pass
```

## 4. Quality Control Checklist

### 4.1 Pre-Implementation
- [ ] All requirements explicitly defined
- [ ] Data schema documented
- [ ] Error scenarios identified
- [ ] Test cases written
- [ ] Performance metrics defined

### 4.2 During Implementation
- [ ] Following code structure template
- [ ] Implementing all error handlers
- [ ] Adding required logging
- [ ] Writing documentation
- [ ] Creating unit tests

### 4.3 Post-Implementation
- [ ] All tests passing
- [ ] Performance requirements met
- [ ] Documentation complete
- [ ] Error handling verified
- [ ] Accessibility validated

## 5. Validation Commands

### 5.1 Feature Validation
```powershell
# Required validation commands
$ValidationScript = {
    # Test core functionality
    Test-FeatureCore
    # Validate error handling
    Test-ErrorHandling
    # Check performance
    Measure-Performance
    # Verify accessibility
    Test-Accessibility
}
```

### 5.2 Quality Metrics
```yaml
quality_metrics:
  code_coverage: "minimum 80%"
  performance:
    response_time: "maximum 300ms"
    memory_usage: "maximum threshold"
  accessibility:
    wcag_level: "AA"
    keyboard_navigation: "required"
  error_handling:
    coverage: "100% of identified scenarios"
    recovery: "all critical paths"
```

## 6. Project-Specific Requirements

Add project-specific requirements here, following the same structured format:

```yaml
project_requirements:
  technical_stack:
    - language: "version"
    - framework: "version"
  features:
    - name: "Feature"
      priority: "Priority level"
      requirements: ["list", "of", "requirements"]
  constraints:
    - type: "Constraint type"
      details: "Specific details"
```

## Usage Instructions

1. Copy this template for each new feature
2. Fill in all required sections
3. Use as validation checklist during implementation
4. Verify against quality metrics
5. Document any deviations with justification

## Enforcement

These rules should be enforced through:
1. Code review guidelines
2. Automated validation scripts
3. CI/CD pipeline checks
4. Documentation requirements
5. Testing coverage requirements
