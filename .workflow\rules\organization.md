# Code Organization Rules

## 1. File Structure
- Place utilities in `utils/` directory
- UI components go in `gui/` directory
- Keep main application logic in `app.py`
- Store tests in `tests/` directory matching source structure

## 2. Class Organization
- Separate concerns into dedicated classes
- Use manager classes for related functionality
- Follow single responsibility principle
- Keep UI and business logic separate

## 3. Error Handling
- Use centralized ErrorManager for consistency
- Log errors for debugging
- Provide user-friendly error messages
- Implement graceful error recovery

## 4. Documentation
- Document all public methods and classes
- Include usage examples in docstrings
- Maintain README with setup instructions
- Document UI component relationships

## 5. Code Style
- Follow PEP 8 guidelines
- Use consistent naming conventions
- Implement type hints
- Keep methods focused and concise

## 6. Testing
- Write unit tests for utilities
- Include integration tests for UI
- Test error handling paths
- Maintain test coverage
