#!/usr/bin/env python3
"""
Isolated launcher for Documentation Reader
Ensures no conflicts with parent application
"""

import sys
import os
from pathlib import Path

# Get current directory
current_dir = Path(__file__).parent.absolute()

# Preserve essential Python paths but prioritize current directory
original_path = sys.path.copy()
sys.path.clear()
sys.path.extend([
    str(current_dir),  # Current directory first
    *[p for p in original_path if any(x in p.lower() for x in ['site-packages', 'python3', 'lib', 'dlls'])]
])

# Clear any cached modules that might conflict
modules_to_clear = [name for name in sys.modules.keys() if any(x in name.lower() for x in ['youtube', 'api', 'bookmark'])]
for module in modules_to_clear:
    if module in sys.modules:
        del sys.modules[module]

print("🚀 Starting Documentation Reader (Isolated Mode)")
print(f"📁 Working directory: {current_dir}")
print(f"🐍 Python path: {sys.path[:3]}...")

try:
    # Import PyQt5
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    print("✅ PyQt5 imported successfully")

    # Import our components
    from doc_reader_app import DocumentationReaderApp
    from markdown_renderer import MarkdownRenderer
    from file_explorer import FileExplorer
    print("✅ Documentation Reader components imported")

    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Documentation Reader")
    app.setStyle('Fusion')

    # Set application font
    font = QFont("Segoe UI", 13)
    app.setFont(font)

    # Apply dark theme
    dark_stylesheet = """
    QMainWindow {
        background-color: #2b2b2b;
        color: #ffffff;
    }
    QWidget {
        background-color: #2b2b2b;
        color: #ffffff;
        selection-background-color: #3daee9;
    }
    QTreeWidget {
        background-color: #3c3c3c;
        border: 1px solid #555555;
        border-radius: 5px;
        font-size: 15px;
        padding: 8px;
        alternate-background-color: #3c3c3c;
        outline: none;
    }

    QTreeWidget::item {
        padding: 12px 8px;
        border: none;
        color: #ffffff;
        font-size: 15px;
        min-height: 28px;
        font-weight: 500;
        background-color: transparent;
    }

    QTreeWidget::item:hover {
        background-color: #4a4a4a;
        border-radius: 4px;
    }

    QTreeWidget::item:selected {
        background-color: #3daee9;
        border-radius: 4px;
        color: #ffffff;
    }

    QTreeWidget::item:selected:hover {
        background-color: #2980b9;
    }

    QTreeWidget::branch {
        background-color: transparent;
    }

    QTreeWidget::branch:has-children:!has-siblings:closed,
    QTreeWidget::branch:closed:has-children:has-siblings {
        border-image: none;
        image: url(none);
    }

    QTreeWidget::branch:open:has-children:!has-siblings,
    QTreeWidget::branch:open:has-children:has-siblings {
        border-image: none;
        image: url(none);
    }
    QTextEdit {
        background-color: #ffffff;
        color: #000000;
        border: 1px solid #555555;
        border-radius: 5px;
    }

    QSplitter {
        background-color: #2b2b2b;
        border: none;
    }

    QSplitter::handle {
        background-color: #555555;
        width: 3px;
        margin: 2px;
        border-radius: 1px;
    }

    QSplitter::handle:hover {
        background-color: #3daee9;
    }

    QLabel {
        color: #ffffff;
        font-size: 14px;
        font-weight: 500;
    }
    QPushButton {
        background-color: #4a4a4a;
        border: 1px solid #666666;
        border-radius: 5px;
        padding: 14px 22px;
        color: #ffffff;
        font-size: 16px;
        font-weight: bold;
        min-height: 30px;
        min-width: 100px;
    }
    QPushButton:hover {
        background-color: #5a5a5a;
        border-color: #3daee9;
    }
    """
    # Theme will be applied by the settings manager
    # app.setStyleSheet(dark_stylesheet)

    # Create main window
    docs_root = current_dir.parent  # docs_V0 directory
    window = DocumentationReaderApp(docs_root)
    window.show()

    # Center window
    screen = app.desktop().screenGeometry()
    size = window.geometry()
    window.move(
        (screen.width() - size.width()) // 2,
        (screen.height() - size.height()) // 2
    )

    print("✅ Documentation Reader window created and shown")
    print("🎉 Application ready! Check your screen for the window.")

    # Start event loop
    sys.exit(app.exec_())

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure PyQt5 is installed: pip install PyQt5")

except Exception as e:
    print(f"❌ Error starting application: {e}")
    import traceback
    traceback.print_exc()
