# PowerShell script for code validation
param (
    [switch]$ValidateAll = $false
)

# Import configuration
$CONFIG = Get-Content -Path "..\rules\validation_rules.md" | ConvertFrom-Yaml

# Setup Python environment
$ENV:PYTHONPATH = "$PSScriptRoot\..\.."

Write-Host "Starting code validation..." -ForegroundColor Green

# Run linting checks
Write-Host "Running pylint checks..." -ForegroundColor Blue
python -m pylint ../.. --rcfile=../../.pylintrc

# Run type checking
Write-Host "Running type checks..." -ForegroundColor Blue
python -m mypy ../.. --config-file ../../mypy.ini

# Run unit tests
Write-Host "Running unit tests..." -ForegroundColor Blue
python -m pytest ../../tests --cov=../../ --cov-report=xml

# Validate documentation
Write-Host "Checking documentation..." -ForegroundColor Blue
python ./doc_validator.py

# Performance checks if ValidateAll is specified
if ($ValidateAll) {
    Write-Host "Running performance tests..." -ForegroundColor Blue
    python ./performance_validator.py
}

Write-Host "Validation complete!" -ForegroundColor Green
