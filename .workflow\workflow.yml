name: Unified Validation Workflow

# Workflow triggers
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

# Environment configuration
env:
  PYTHON_VERSION: '3.13'
  VALIDATION_TOOLS: './validators'
  RULES_PATH: './rules'

jobs:
  validate:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Validate Code
      run: |
        pwsh -File ${{ env.VALIDATION_TOOLS }}/validate_code.ps1
        
    - name: Validate Features
      run: |
        pwsh -File ${{ env.VALIDATION_TOOLS }}/validate_features.ps1
        
    - name: Run Tests
      run: |
        python -m unittest discover -s tests
        
    - name: Generate Report
      if: always()
      run: |
        python ${{ env.VALIDATION_TOOLS }}/generate_report.py
        
    - name: Upload Report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: validation-report
        path: reports/validation_report.md
