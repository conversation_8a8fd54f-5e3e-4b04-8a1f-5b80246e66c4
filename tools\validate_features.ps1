# Main validation script for YouTube Explorer project
param(
    [string]$ProjectRoot = ".",
    [switch]$ValidateAll,
    [string]$FeaturePath,
    [switch]$ValidateCodeQuality
)

# Import required modules
$ErrorActionPreference = "Stop"

function Install-PythonDependencies {
    Write-Host "Installing required Python packages..."
    try {
        python -m pip install pyyaml
    } catch {
        Write-Error "Failed to install Python dependencies: $_"
        exit 1
    }
}

# Main execution
try {
    # Install dependencies
    Install-PythonDependencies

    $command = "python -m tools.main_validator --project-root `"$ProjectRoot`""

    if ($ValidateAll) {
        $command += " --validate-all"
    }
    if ($FeaturePath) {
        $command += " --feature-path `"$FeaturePath`""
    }
    if ($ValidateCodeQuality) {
        $command += " --validate-code-quality"
    }

    Write-Host "Executing validation command: $command"
    Invoke-Expression $command

    if ($LASTEXITCODE -ne 0) {
        Write-Error "Validation failed!"
        exit 1
    }
    else {
        Write-Host "All requested validations passed successfully!" -ForegroundColor Green
    }
}
catch {
    Write-Error "An error occurred during validation: $_"
    exit 1
}
