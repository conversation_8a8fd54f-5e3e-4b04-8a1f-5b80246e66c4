# Project Update Workflow Pattern

## 1. Pre-Update Analysis
```markdown
1. Current State Review:
   - Document current functionality
   - List validated features
   - Identify critical components
   - Map data flows and dependencies

2. Prompt Analysis:
   - Extract new requirements
   - Identify changes to existing features
   - List new features
   - Note any architectural changes
   
3. Impact Assessment:
   - Map affected components
   - Identify risky changes
   - List dependencies to preserve
   - Note core functionality to protect
```

## 2. Update Strategy

### Phase 1: Safety Net
```python
def prepare_update():
    """
    1. Create feature validation snapshots
    2. Document current test coverage
    3. Save current state markers
    4. Create rollback points
    """
    
class UpdateValidator:
    def validate_core_features(self):
        """Validate core functionality preservation"""
        
    def check_data_integrity(self):
        """Ensure data handling remains intact"""
        
    def verify_ui_behavior(self):
        """Verify UI interactions work as before"""
```

### Phase 2: Incremental Updates
```python
class UpdateManager:
    """Manages the update process safely"""
    
    def stage_changes(self):
        """Group changes by risk level"""
        self.low_risk = []    # UI tweaks, documentation
        self.medium_risk = [] # Feature enhancements
        self.high_risk = []   # Core changes, data flow
        
    def apply_changes(self):
        """Apply changes in order of safety"""
        self._apply_low_risk()
        self._validate_state()
        self._apply_medium_risk()
        self._validate_state()
        self._apply_high_risk()
        self._final_validation()
```

## 3. Validation Framework

### Core Functionality Checklist
```markdown
- [ ] Data persistence works
- [ ] UI responds correctly
- [ ] Error handling intact
- [ ] Performance acceptable
- [ ] User preferences preserved
```

### Update Validation Tests
```python
def test_backwards_compatibility():
    """Ensure old features still work"""
    
def test_data_migration():
    """Verify data handling"""
    
def test_error_recovery():
    """Check error handling"""
```

## 4. Rollback Protocol
```python
class RollbackManager:
    """Manages safe rollback if needed"""
    
    def create_snapshot(self):
        """Save current state"""
        
    def validate_snapshot(self):
        """Verify snapshot integrity"""
        
    def restore_if_needed(self):
        """Restore from snapshot if issues"""
```

## Best Practices

1. Documentation First:
   - Document current state
   - Map dependencies
   - List critical paths
   - Note user expectations

2. Incremental Changes:
   - Start with lowest risk
   - Validate each step
   - Keep changes atomic
   - Test between changes

3. Data Protection:
   - Backup user data
   - Verify data integrity
   - Test migrations
   - Preserve preferences

4. UI Consistency:
   - Maintain patterns
   - Keep shortcuts working
   - Preserve layouts
   - Match existing style

5. Error Handling:
   - Keep error paths
   - Test recovery
   - Update messages
   - Log changes
