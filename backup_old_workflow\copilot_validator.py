import os
import ast
import json
import yaml
import inspect
from typing import Dict, List, Any
from dataclasses import dataclass

@dataclass
class FeatureValidation:
    name: str
    requirements_met: bool
    missing_requirements: List[str]
    error_handling_score: float
    documentation_score: float
    test_coverage: float

class CopilotRulesValidator:
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.rules = self.load_rules()
        
    def load_rules(self) -> Dict[str, Any]:
        """Load Copilot rules from COPILOT_RULES.md"""
        rules_file = os.path.join(self.project_root, 'COPILOT_RULES.md')
        if not os.path.exists(rules_file):
            raise FileNotFoundError("COPILOT_RULES.md not found")
        
        # Parse the rules file
        with open(rules_file, 'r') as f:
            content = f.read()
            # Extract YAML blocks and parse them
            yaml_blocks = []
            for block in content.split('```yaml')[1:]:
                yaml_content = block.split('```')[0]
                yaml_blocks.append(yaml.safe_load(yaml_content))
                
        return {'sections': yaml_blocks}

    def validate_feature(self, feature_path: str) -> FeatureValidation:
        """Validate a feature implementation against Copilot rules"""
        with open(feature_path, 'r') as f:
            content = f.read()
            
        # Parse the Python code
        tree = ast.parse(content)
        
        # Initialize validation results
        validation = FeatureValidation(
            name=os.path.basename(feature_path),
            requirements_met=True,
            missing_requirements=[],
            error_handling_score=0.0,
            documentation_score=0.0,
            test_coverage=0.0
        )
        
        # Validate structure
        self._validate_structure(tree, validation)
        # Validate documentation
        self._validate_documentation(tree, validation)
        # Validate error handling
        self._validate_error_handling(tree, validation)
        
        return validation

    def _validate_structure(self, tree: ast.AST, validation: FeatureValidation):
        """Validate code structure requirements"""
        required_methods = {'__init__', 'validate_requirements', 
                          'setup_error_handling', 'initialize_logging'}
        
        found_methods = set()
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                found_methods.add(node.name)
        
        missing = required_methods - found_methods
        if missing:
            validation.requirements_met = False
            validation.missing_requirements.extend(
                [f"Missing required method: {method}" for method in missing]
            )

    def _validate_documentation(self, tree: ast.AST, validation: FeatureValidation):
        """Validate documentation requirements"""
        doc_score = 0
        total_functions = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                total_functions += 1
                if ast.get_docstring(node):
                    docstring = ast.get_docstring(node)
                    # Check for required sections
                    required_sections = ['Purpose:', 'Requirements:', 
                                      'Parameters:', 'Returns:', 'Raises:']
                    found_sections = sum(
                        1 for section in required_sections 
                        if section in docstring
                    )
                    doc_score += found_sections / len(required_sections)
        
        validation.documentation_score = (
            doc_score / total_functions if total_functions > 0 else 0
        )

    def _validate_error_handling(self, tree: ast.AST, validation: FeatureValidation):
        """Validate error handling implementation"""
        try_blocks = 0
        error_handlers = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Try):
                try_blocks += 1
                error_handlers += len(node.handlers)
        
        # Calculate error handling score based on coverage
        validation.error_handling_score = (
            error_handlers / try_blocks if try_blocks > 0 else 0
        )

    def generate_report(self, validations: List[FeatureValidation]) -> str:
        """Generate a validation report"""
        report = ["# Feature Validation Report\n"]
        
        for validation in validations:
            report.append(f"## {validation.name}\n")
            report.append(f"Requirements Met: {validation.requirements_met}\n")
            
            if validation.missing_requirements:
                report.append("\nMissing Requirements:\n")
                for req in validation.missing_requirements:
                    report.append(f"- {req}\n")
            
            report.append(f"\nScores:\n")
            report.append(f"- Documentation: {validation.documentation_score:.2%}\n")
            report.append(f"- Error Handling: {validation.error_handling_score:.2%}\n")
            report.append(f"- Test Coverage: {validation.test_coverage:.2%}\n")
            
            report.append("\n---\n")
        
        return ''.join(report)

def main():
    # Example usage
    validator = CopilotRulesValidator(".")
    
    # Validate all Python files in the project
    validations = []
    for root, _, files in os.walk("."):
        for file in files:
            if file.endswith(".py"):
                path = os.path.join(root, file)
                try:
                    validation = validator.validate_feature(path)
                    validations.append(validation)
                except Exception as e:
                    print(f"Error validating {path}: {e}")
    
    # Generate and save report
    report = validator.generate_report(validations)
    with open("validation_report.md", "w") as f:
        f.write(report)

if __name__ == "__main__":
    main()
