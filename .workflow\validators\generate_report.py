#!/usr/bin/env python3
"""Report generator for validation results."""

import sys
import os
from datetime import datetime
from pathlib import Path
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ReportGenerator:
    """Generates validation reports from validation results."""
    
    def __init__(self, output_dir: str = "../reports"):
        """Initialize report generator."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def generate_report(self, validation_results: dict) -> str:
        """Generate markdown report from validation results."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"""# Validation Report
Generated: {timestamp}

## Summary
- Total Features: {len(validation_results.get('features', []))}
- Passed: {validation_results.get('passed', 0)}
- Failed: {validation_results.get('failed', 0)}
- Warnings: {validation_results.get('warnings', 0)}

## Details

### Code Quality
```
Pylint Score: {validation_results.get('pylint_score', 'N/A')}
Type Check Status: {'✓' if validation_results.get('type_check_passed') else '✗'}
Test Coverage: {validation_results.get('test_coverage', 'N/A')}%
```

### Documentation Status
```
API Documentation: {'Complete' if validation_results.get('api_docs_complete') else 'Incomplete'}
README Status: {'Up to date' if validation_results.get('readme_updated') else 'Needs update'}
Examples: {'Present' if validation_results.get('examples_provided') else 'Missing'}
```

### Performance Metrics
```
Response Time: {validation_results.get('response_time', 'N/A')}ms
Memory Usage: {validation_results.get('memory_usage', 'N/A')}MB
CPU Usage: {validation_results.get('cpu_usage', 'N/A')}%
```

## Issues Found
"""
        
        # Add any issues found
        issues = validation_results.get('issues', [])
        if issues:
            for issue in issues:
                report += f"- {issue}\n"
        else:
            report += "No issues found.\n"
            
        # Write report to file
        report_path = self.output_dir / f"validation_report_{timestamp.replace(':', '-')}.md"
        report_path.write_text(report)
        
        return str(report_path)

def main():
    """Main function to generate validation report."""
    if len(sys.argv) < 2:
        logger.error("Please provide path to validation results JSON file")
        sys.exit(1)
        
    results_path = sys.argv[1]
    
    try:
        with open(results_path, 'r') as f:
            results = json.load(f)
    except Exception as e:
        logger.error(f"Failed to load validation results: {e}")
        sys.exit(1)
        
    generator = ReportGenerator()
    report_path = generator.generate_report(results)
    logger.info(f"Report generated: {report_path}")

if __name__ == '__main__':
    main()
