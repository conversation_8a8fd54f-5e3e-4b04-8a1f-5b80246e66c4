{"timestamp": "2025-06-17T09:56:53.391888", "response": {"kind": "youtube#playlistItemListResponse", "etag": "6p7XClR-tGCtLdWyyh1EPQCWFq8", "nextPageToken": "EAAaHlBUOkNBb2lFRFV6T1RaQk1ERXhPVE0wT1Rnd09FVQ", "items": [{"kind": "youtube#playlistItem", "etag": "RhiN2MQLOD3m3PaAN-_62IMrIHs", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi41NkI0NEY2RDEwNTU3Q0M2", "snippet": {"publishedAt": "2020-01-12T12:37:43Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "002 The Model Environment", "description": "AVEVA\\E3D\n1 AVEVA Everything3D™ 2.1\n2 AVEVA E3D™ 2.1 - Introduction to Model & Terminology\n1 Getting Started", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/f4K9S0IgkUk/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/f4K9S0IgkUk/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/f4K9S0IgkUk/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/f4K9S0IgkUk/sddefault.jpg", "width": 640, "height": 480}}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 0, "resourceId": {"kind": "youtube#video", "videoId": "f4K9S0IgkUk"}, "videoOwnerChannelTitle": "Engineering", "videoOwnerChannelId": "UCfDSM6gWLMAAjGrjUbsQIgw"}}, {"kind": "youtube#playlistItem", "etag": "GSeYgxsZ8jyYjDE5JjWngmbqK58", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi4yODlGNEE0NkRGMEEzMEQy", "snippet": {"publishedAt": "2020-01-12T12:45:42Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "REFLUX DRUM SADDLE in Everything 3D", "description": "Reflux Drum Saddle\n\naveva everything 3d\ne3d\nreflux drum", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/gENFmVVuQaY/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/gENFmVVuQaY/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/gENFmVVuQaY/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/gENFmVVuQaY/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/gENFmVVuQaY/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 1, "resourceId": {"kind": "youtube#video", "videoId": "gENFmVVuQaY"}, "videoOwnerChannelTitle": "Designing Tutorials", "videoOwnerChannelId": "UCEaEMbvc15oJ-YUf68HxCMQ"}}, {"kind": "youtube#playlistItem", "etag": "e-oaRgELGTVNM2GE6IzF-aUKlQU", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi4wMTcyMDhGQUE4NTIzM0Y5", "snippet": {"publishedAt": "2020-01-12T13:15:55Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "Clash Manager (E3D)", "description": "Identify and resolve clashes in the 3D model", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/1JfJ3Sbd9BY/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/1JfJ3Sbd9BY/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/1JfJ3Sbd9BY/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/1JfJ3Sbd9BY/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/1JfJ3Sbd9BY/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 2, "resourceId": {"kind": "youtube#video", "videoId": "1JfJ3Sbd9BY"}, "videoOwnerChannelTitle": "Synergy Engineering. PT", "videoOwnerChannelId": "UC0dAmlls1hyU_OcoQCmFzRg"}}, {"kind": "youtube#playlistItem", "etag": "GE2D0T4gS-F8WATCaAs2Im924vs", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi41MjE1MkI0OTQ2QzJGNzNG", "snippet": {"publishedAt": "2020-01-12T13:23:12Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "Creating a Pipe Support (E3D)", "description": "Create a pipe support in E3D with the multidiscipline supports (MDS) feature.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/OyhjFrilDPs/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/OyhjFrilDPs/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/OyhjFrilDPs/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/OyhjFrilDPs/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/OyhjFrilDPs/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 3, "resourceId": {"kind": "youtube#video", "videoId": "OyhjFrilDPs"}, "videoOwnerChannelTitle": "Synergy Engineering. PT", "videoOwnerChannelId": "UC0dAmlls1hyU_OcoQCmFzRg"}}, {"kind": "youtube#playlistItem", "etag": "OGlqqYpY1mcUTRib2sQps9SyLMk", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi4wOTA3OTZBNzVEMTUzOTMy", "snippet": {"publishedAt": "2020-01-12T13:38:42Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "Aveva E3D Piping Tutorial 2020 | How to do Piping Modelling In E3d Tutorial | Bounce Back", "description": "Learn Aveva e3d tutorial, Aveva e3d training, Aveva e3d piping tutorial, Aveva e3d equipment modelling.\n\nLike, Comment and Share\nSubscribe to your channel For Upcoming Videos.\n\n#Avevae3d #avevae3dpiping #avevae3dtutorial", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/0UKcrKJnYPE/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/0UKcrKJnYPE/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/0UKcrKJnYPE/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/0UKcrKJnYPE/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/0UKcrKJnYPE/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 4, "resourceId": {"kind": "youtube#video", "videoId": "0UKcrKJnYPE"}, "videoOwnerChannelTitle": "<PERSON><PERSON><PERSON>", "videoOwnerChannelId": "UCldg4hAylsOoKTuFnEKPbFA"}}, {"kind": "youtube#playlistItem", "etag": "ddmhKu_e1OZ4kz8WrYYxAEpkCu0", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi41MzJCQjBCNDIyRkJDN0VD", "snippet": {"publishedAt": "2020-01-12T14:13:16Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "E3d Piping Tutorial Basic | Aveva E3d Piping Modelling 2020", "description": "E3d Piping Tutorial, Basic Aveva E3d Piping Modelling 2019, Column Piping.\n\n#avevae3d  #e3dpiping #e3dpipingtutorial", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/uSsOVRqgSnk/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/uSsOVRqgSnk/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/uSsOVRqgSnk/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/uSsOVRqgSnk/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/uSsOVRqgSnk/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 5, "resourceId": {"kind": "youtube#video", "videoId": "uSsOVRqgSnk"}, "videoOwnerChannelTitle": "<PERSON><PERSON><PERSON>", "videoOwnerChannelId": "UCldg4hAylsOoKTuFnEKPbFA"}}, {"kind": "youtube#playlistItem", "etag": "-AgpEn_5tgVP0IhOlK0L-3la1jc", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi40NzZCMERDMjVEN0RFRThB", "snippet": {"publishedAt": "2020-03-09T11:50:11Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "ORINOX Formation - AVEVA EVERYTHING 3D (E3D) Training course", "description": "The engineering experience of our training specialists enables them to understand and respond to the specific needs of our customers. \nOrinox training specialists have years of experience in the field of plant and piping design. In between training sessions, they continue to work as engineers and design managers on both internal and external projects for our clients. \nOrinox training specialists are regularly confronted with the same challenges as our customers, so our training materials stay relevant and up to date.\n\nWe train designers of complex systems to adhere to the essential rules of industrial design. AVEVA PDMS & E3D is a sophisticated software, but essential techniques and processes must be learned and practiced. \nAll Orinox training sessions emphasize the importance of following the process necessary to conceiving industrial plant elements, in order to ensure proper construction, maintenance, and operation.\n\n\nwww.orinox.com/en/services/aveva-pdms-diagrams-training", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/5VeWbfS9Gbk/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/5VeWbfS9Gbk/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/5VeWbfS9Gbk/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/5VeWbfS9Gbk/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/5VeWbfS9Gbk/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 6, "resourceId": {"kind": "youtube#video", "videoId": "5VeWbfS9Gbk"}, "videoOwnerChannelTitle": "ORINOX", "videoOwnerChannelId": "UCfVWqRlBcjls9nCTdcaN9_Q"}}, {"kind": "youtube#playlistItem", "etag": "3ZIxWSdpLUR3rxWn_wGxuhN8qBM", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi5EMEEwRUY5M0RDRTU3NDJC", "snippet": {"publishedAt": "2020-03-22T11:44:06Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "Private video", "description": "This video is private.", "thumbnails": {}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 7, "resourceId": {"kind": "youtube#video", "videoId": "DeWCX3HvADs"}}}, {"kind": "youtube#playlistItem", "etag": "P47YqNU-TbvfxRstxHhaVWyBPE8", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi45ODRDNTg0QjA4NkFBNkQy", "snippet": {"publishedAt": "2020-03-22T11:48:04Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "Pipe Support Drawing (E3D)", "description": "Generate pipe support drawings in E3D", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/hxv1dOwZENU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/hxv1dOwZENU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/hxv1dOwZENU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/hxv1dOwZENU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/hxv1dOwZENU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 8, "resourceId": {"kind": "youtube#video", "videoId": "hxv1dOwZENU"}, "videoOwnerChannelTitle": "Synergy Engineering. PT", "videoOwnerChannelId": "UC0dAmlls1hyU_OcoQCmFzRg"}}, {"kind": "youtube#playlistItem", "etag": "Z5murCWjpKZC1e5BUlQVWIkDN7U", "id": "UEw2UjFOU3F2cE5ydEoxdEFyeUljaWRGM1lOMVdlTm9lWi4zMDg5MkQ5MEVDMEM1NTg2", "snippet": {"publishedAt": "2020-03-22T11:49:26Z", "channelId": "UCdtYn-OHJs1RUWlXGgbe-jg", "title": "Create and modify # pipe support on Aveva #Bocad steel in Aveva #E3D", "description": "", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/y6WrID5pdb0/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/y6WrID5pdb0/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/y6WrID5pdb0/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/y6WrID5pdb0/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/y6WrID5pdb0/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "RITABRATA DEY", "playlistId": "PL6R1NSqvpNrtJ1tAryIcidF3YN1WeNoeZ", "position": 9, "resourceId": {"kind": "youtube#video", "videoId": "y6WrID5pdb0"}, "videoOwnerChannelTitle": "quang tonthat", "videoOwnerChannelId": "UCgFG-xTvgq4t3J28biRXnQg"}}], "pageInfo": {"totalResults": 25, "resultsPerPage": 10}}}