"""
Utility functions for symbol handling, tooltips, and error management
"""
import tkinter as tk
from tkinter import ttk, messagebox
import logging
from typing import Dict, Optional, Callable

class SymbolManager:
    """Manages Unicode symbols with fallbacks"""
    
    SYMBOLS = {
        'bookmark': {
            'unicode': "★",
            'fallback': "*",
            'code': "U+2605"
        },
        'unbookmark': {
            'unicode': "☆",
            'fallback': "o",
            'code': "U+2606"
        },
        'delete': {
            'unicode': "🗑️",
            'fallback': "X",
            'code': "U+1F5D1"
        },
        'add_collection': {
            'unicode': "📁",
            'fallback': "[+]",
            'code': "U+1F4C1"
        },
        'edit': {
            'unicode': "✏️",
            'fallback': "[E]",
            'code': "U+270F"
        },
        'search': {
            'unicode': "🔍",
            'fallback': "[S]",
            'code': "U+1F50D"
        }
    }
    
    def get_symbol(self, name: str, fallback: Optional[str] = None) -> str:
        """
        Retrieves a symbol by name. If the Unicode symbol is not displayable,
        it falls back to a predefined ASCII character or a custom fallback.
        
        Args:
            name: The name of the symbol (e.g., 'bookmark', 'delete').
            fallback: An optional custom fallback character if the symbol is not found or displayable.
            
        Returns:
            The Unicode symbol if displayable, otherwise its fallback or the custom fallback.
        """
        symbol_info = self.SYMBOLS.get(name)
        if not symbol_info:
            return fallback if fallback is not None else '?'  # Default fallback for unknown symbols
        
        unicode_symbol = symbol_info['unicode']
        
        # Try to use Unicode symbol, fall back if not displayable
        if self._is_displayable(unicode_symbol):
            return unicode_symbol
        else:
            return fallback if fallback is not None else symbol_info['fallback']

    def _is_displayable(self, symbol: str) -> bool:
        """
        Checks if a given Unicode symbol can be properly displayed by Tkinter.
        This is done by attempting to render the symbol in a hidden Tkinter widget
        and checking if it has a non-zero width and height.
        """
        root = None
        try:
            root = tk.Tk()
            root.withdraw()  # Hide the main window
            
            # Create a temporary label to test symbol rendering
            label = tk.Label(root, text=symbol, font=("TkDefaultFont", 12))
            label.pack()
            root.update_idletasks()  # Force update to get dimensions
            
            # Check if the label has a measurable size
            is_displayable = label.winfo_width() > 0 and label.winfo_height() > 0
            
            label.destroy()
            return is_displayable
        except tk.TclError as e:
            # This might happen if Tkinter cannot handle the symbol at all
            logging.warning(f"Tkinter TclError when checking symbol '{symbol}': {e}")
            return False
        except Exception as e:
            logging.error(f"Unexpected error checking symbol '{symbol}': {e}")
            return False
        finally:
            if root:
                root.destroy()  # Ensure the temporary root is destroyed

class TooltipManager:
    """Manages tooltips with consistent timing and positioning"""
    
    def __init__(self, delay: int = 300):
        """
        Args:
            delay: Tooltip display delay in milliseconds
        """
        self.delay = delay
        self._tooltip = None
        self._timer = None
        self.tooltip_window = None
    
    def add_tooltip(self, widget: tk.Widget, text: str,
                   position: Optional[Callable] = None):
        """
        Add a tooltip to a widget
        
        Args:
            widget: The widget to add tooltip to
            text: Tooltip text
            position: Optional position calculation function
        """
        def show(event):
            if self._timer:
                widget.after_cancel(self._timer)
            
            def display():
                if self.tooltip_window:
                    return
                    
                x = widget.winfo_rootx() + 25
                y = widget.winfo_rooty() + 25
                
                self.tooltip_window = tk.Toplevel(widget)
                self.tooltip_window.wm_overrideredirect(True)
                self.tooltip_window.wm_geometry(f"+{x}+{y}")
                
                label = tk.Label(
                    self.tooltip_window,
                    text=text,
                    background="lightyellow",
                    relief="solid",
                    borderwidth=1,
                    font=("TkDefaultFont", "8", "normal")
                )
                label.pack()
            
            self._timer = widget.after(self.delay, display)
        
        def hide(event):
            if self._timer:
                widget.after_cancel(self._timer)
            if hasattr(self, 'tooltip_window') and self.tooltip_window:
                self.tooltip_window.destroy()
                self.tooltip_window = None
        
        widget.bind('<Enter>', show)
        widget.bind('<Leave>', hide)

class ErrorManager:
    """Centralized error handling and user feedback"""
    
    ERROR_MESSAGES = {
        'api_limit': "YouTube API limit reached. Please try again later.",
        'network': "Unable to connect. Check your internet connection.",
        'symbols': "Unable to display symbols. Using simple alternatives.",
        'data': "Error loading bookmarks. Data may be corrupted."
    }
    
    def __init__(self, parent: tk.Widget):
        self.parent = parent
        self.logger = logging.getLogger(__name__)
    
    def show_error(self, error_type: str, message: str, details: Optional[str] = None):
        """
        Display error message to user
        
        Args:
            error_type: Type of error ('api', 'network', 'data', etc.)
            message: Error message to display
            details: Optional detailed error information
        """
        # Log the error
        log_message = f"Error [{error_type}]: {message}"
        if details:
            log_message += f" - Details: {details}"
        self.logger.error(log_message)
        
        # Get user-friendly message
        user_message = self.ERROR_MESSAGES.get(error_type, message)
        
        # Show dialog
        messagebox.showerror("Error", user_message)
    
    def show_warning(self, title: str, message: str):
        """Show warning dialog"""
        self.logger.warning(f"Warning [{title}]: {message}")
        messagebox.showwarning(title, message)
    
    def show_info(self, title: str, message: str):
        """Show info dialog"""
        self.logger.info(f"Info [{title}]: {message}")
        messagebox.showinfo(title, message)

class AnimationManager:
    """Manages UI animations and visual feedback."""

    def __init__(self, master: tk.Tk, logger: logging.Logger):
        self.master = master
        self.logger = logger

    def flash_widget(self, treeview: ttk.Treeview, item_id: str, color: str, duration: int):
        """
        Flashes a treeview item with a given color for a specified duration.
        """
        try:
            original_tags = treeview.item(item_id, "tags")
            treeview.item(item_id, tags=original_tags + ("flash",))
            treeview.tag_configure("flash", background=color)

            def reset_color():
                try:
                    treeview.item(item_id, tags=original_tags)
                    treeview.tag_configure("flash", background="")
                except tk.TclError:
                    # Item may have been deleted
                    pass

            self.master.after(duration, reset_color)
        except Exception as e:
            self.logger.error(f"Failed to flash widget: {e}")
