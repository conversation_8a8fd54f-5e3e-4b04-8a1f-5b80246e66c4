@echo off
title YouTube Explorer Documentation Reader
echo.
echo ?? YouTube Explorer Documentation Reader
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ? Python not found in PATH
    echo Please install Python 3.6+ and add it to your PATH
    echo.
    pause
    exit /b 1
)

echo ? Python found
echo.

REM Try to run the launcher
echo ?? Starting Documentation Reader...
echo.

python launch.py

if errorlevel 1 (
    echo.
    echo ? Application failed to start
    echo.
    echo ?? Try running manually:
    echo    python main.py
    echo.
)

pause
