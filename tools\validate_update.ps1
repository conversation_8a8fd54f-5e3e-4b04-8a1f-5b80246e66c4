# Validate code before applying changes
param(
    [string]$ProjectRoot = (Get-Item $PSScriptRoot).Parent.FullName
)

Write-Host "Running code validation..." -ForegroundColor Cyan

# Run Python validation script
$ValidationScript = Join-Path $ProjectRoot "tools\validate_code.py"
$Result = python $ValidationScript

if ($LASTEXITCODE -eq 0) {
    Write-Host "Code validation passed!" -ForegroundColor Green
    
    # Run the application for final validation
    Write-Host "`nStarting application for final validation..." -ForegroundColor Cyan
    $MainScript = Join-Path $ProjectRoot "main.py"
    python $MainScript
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`nApplication validation successful!" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "`nApplication validation failed!" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "Code validation failed! Please fix the issues and try again." -ForegroundColor Red
    Write-Host $Result
    exit 1
}
