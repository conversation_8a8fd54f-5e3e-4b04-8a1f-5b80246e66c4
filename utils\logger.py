import logging
import sys
from typing import Optional

class AppLogger:
    """
    Centralized logging for the YouTube Explorer application.
    Follows Copilot rules for proper error tracking and debugging.
    """
    
    def __init__(self, name: str = "YouTubeExplorer"):
        self.logger = logging.getLogger(name)
        self.setup_logging()
        
    def setup_logging(self) -> None:
        """Configure logging with both file and console handlers"""
        self.logger.setLevel(logging.INFO)
        
        # File handler for persistent logs
        file_handler = logging.FileHandler("youtube_explorer.log")
        
    def info(self, msg: str):
        """Log info message"""
        self.logger.info(msg)
        
    def error(self, msg: str):
        """Log error message"""
        self.logger.error(msg)
        
    def warning(self, msg: str):
        """Log warning message"""
        self.logger.warning(msg)
        
    def debug(self, msg: str):
        """Log debug message"""
        self.logger.debug(msg)
        file_handler.setLevel(logging.DEBUG)
        file_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_format)
        
        # Console handler for immediate feedback
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_format = logging.Formatter(
            '%(levelname)s: %(message)s'
        )
        console_handler.setFormatter(console_format)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def log_error(self, error: Exception, context: Optional[str] = None) -> None:
        """Log error with context and stack trace"""
        if context:
            self.logger.error(f"{context} - {str(error)}", exc_info=True)
        else:
            self.logger.error(str(error), exc_info=True)
    
    def log_api_call(self, method: str, success: bool, details: Optional[str] = None) -> None:
        """Log API calls for monitoring quota usage"""
        status = "SUCCESS" if success else "FAILED"
        message = f"API Call [{method}] - {status}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_user_action(self, action: str, details: Optional[str] = None) -> None:
        """Log user actions for analytics and debugging"""
        message = f"User Action: {action}"
        if details:
            message += f" - {details}"
        self.logger.debug(message)
    
    def log_performance(self, operation: str, duration_ms: float) -> None:
        """Log performance metrics"""
        self.logger.debug(f"Performance - {operation}: {duration_ms:.2f}ms")
