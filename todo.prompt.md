Build a YouTube Explorer application with the following core features:

1. Video Search & Discovery
- Implement keyword-based search using YouTube Data API v3
- Display search results with video thumbnails, titles, and metadata
- Enable browsing videos by predefined categories/genres
- Show relevant video details (views, duration, upload date, channel)
- Allow users to use different search modes with radio buttons for: playlists, videos, or both
- Use double relational panels to display the search results for videos and playlists
- When users select a playlist in the playlists panel, its videos are displayed in the videos panel
- Double-clicking a video or playlist in any panel (including bookmarks) opens the respective video or playlist in the YouTube browser
- Add a custom column with Unicode symbols (★/☆) to toggle bookmarked and unbookmarked status in both videos and playlists panels, with tooltips for better user feedback
- On each panel, add a toolbar with a "Remove Selected" button (🗑️) and support Delete key for removing items
- Add right-click context menu for quick actions (bookmark/unbookmark, remove)

2. Bookmarking System
- Allow users to bookmark individual videos and playlists using:
  * Click on bookmark symbol (★/☆) in the dedicated column
  * Right-click context menu option
  * Keyboard shortcut (e.g., 'B' to toggle bookmark)
- Organize bookmarks into custom collections/folders
- Store bookmarks persistently (local storage or database)
- Support adding/removing bookmarks with visual feedback and tooltips
- Include bookmark timestamp and notes feature
- Display a list of all user bookmarks in a dedicated sidebar/panel
- Show columns for Title, Type (video or playlist), Timestamp (when bookmarked), and Notes (user-added)
- Allow users to view and manage their saved videos and playlists in one place
- Provide clear visual feedback for bookmark actions with animations or color changes

3. User Interface Requirements
- Use Python's standard Tkinter library for GUI
- Clean, responsive layout following Material Design guidelines
- Search bar with autocomplete suggestions
- Filter and sort options for search results
- Grid/list view toggle for video displays
- Bookmark management sidebar or dedicated page
- Use pandas DataFrame to populate the Treeview panel for displaying search results and bookmarks
- Implement user-friendly bookmark toggles with:
  * Unicode symbols (★/☆) for visual clarity
  * Tooltips on hover
  * Right-click context menus
  * Keyboard shortcuts
- Add panel management controls:
  * Toolbar with clear/remove buttons and icons
  * Right-click context menu for item removal
  * Keyboard shortcuts (Delete key support)
  * Clear all option in panel header

4. Technical Constraints
- Use official YouTube API quota limits appropriately
- Implement user authentication for bookmark persistence
- Cache search results to minimize API calls
- Support offline access to bookmarked content metadata
- Ensure GDPR compliance for user data storage

5. Optional Enhancements
- Playlist creation from bookmarked videos
- Watch history tracking
- Video recommendations based on bookmarks
- Export/import bookmark functionality
- Share bookmarks/collections feature

6. Clean Code Practices
- Follow clean code principles: meaningful naming, modular design, and clear documentation
- Use consistent coding standards and avoid code duplication

7. Project Architecture and Implementation Plan
- Define a modular project architecture with clear separation of concerns:
  - GUI Layer: Tkinter-based interface for user interaction
  - Logic Layer: Handles business logic, API interactions, and data processing
  - Data Layer: Manages persistent storage and caching
- Implementation Steps:
  1. Set up the project structure with folders for GUI, logic, and data layers
  2. Implement YouTube Data API integration for search and video details
  3. Design and develop the Tkinter-based GUI with search, filter, and bookmark features
  4. Create a bookmarking system with persistent storage
  5. Add optional enhancements like playlist creation and video recommendations
  6. Integrate caching and offline support for bookmarked content
  7. Implement user authentication and GDPR-compliant data handling
  8. Test each module independently and then as an integrated system
  9. Add logging and validation systems for robust error handling and debugging
  10. Finalize and deploy the application

8. Testing, Logging, and Validation
- Develop simple and robust testing systems for each module
- Use logging to track application behavior and errors
- Validate user inputs and API responses to ensure data integrity

Deliverables must include proper error handling, loading states, and accessibility features.