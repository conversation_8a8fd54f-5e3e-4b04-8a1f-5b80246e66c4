"""Validates Python code updates and maintains code quality."""

import ast
import inspect
from pathlib import Path
from typing import Dict, List, Set, Type, Any
import logging

class CodeValidator:
    """Validates code changes and updates."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.logger = logging.getLogger(__name__)
        
    def validate_syntax(self, code: str, file_path: str) -> List[str]:
        """Validate Python code syntax.
        
        Args:
            code: The code to validate
            file_path: Path to the file for context
            
        Returns:
            List of syntax issues found
        """
        issues = []
        try:
            # Test compile the code
            compile(code, file_path, 'exec')
            
            # Parse AST for deeper analysis
            tree = ast.parse(code)
            
            # Validate method definitions
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # Check method has self parameter if in class
                    if node.args.args and node.args.args[0].arg != 'self':
                        issues.append(f"Method {node.name} missing self parameter")
                        
                    # Check docstring exists
                    if not ast.get_docstring(node):
                        issues.append(f"Missing docstring in method {node.name}")
                        
        except SyntaxError as e:
            issues.append(f"Syntax error at line {e.lineno}: {e.msg}")
            
        return issues

    def validate_class(self, cls: Type) -> List[str]:
        """Validate a class implementation.
        
        Args:
            cls: The class to validate
            
        Returns:
            List of validation issues
        """
        issues = []
        
        # Check class docstring
        if not cls.__doc__:
            issues.append(f"Missing docstring in class {cls.__name__}")
            
        # Check required methods
        required_methods = {
            '__init__',
            'run'
        }
        
        for method in required_methods:
            if not hasattr(cls, method):
                issues.append(f"Missing required method: {method}")
                
        # Check method implementations
        for name, method in inspect.getmembers(cls, inspect.isfunction):
            if not name.startswith('_'):  # Public methods
                method_issues = self.validate_method(method)
                issues.extend(method_issues)
                
        return issues
        
    def validate_method(self, method) -> List[str]:
        """Validate a method implementation.
        
        Args:
            method: The method to validate
            
        Returns:
            List of validation issues
        """
        issues = []
        
        try:
            # Get method source
            source = inspect.getsource(method)
            
            # Check error handling
            if 'try:' not in source:
                issues.append(f"Missing error handling in method {method.__name__}")
                
            # Check logging
            if 'self.logger' not in source:
                issues.append(f"Missing logging in method {method.__name__}")
                
            # Check documentation
            if not method.__doc__:
                issues.append(f"Missing docstring in method {method.__name__}")
                
        except Exception as e:
            issues.append(f"Failed to validate method {method.__name__}: {str(e)}")
            
        return issues

    def validate_file(self, file_path: str) -> List[str]:
        """Validate a Python source file.
        
        Args:
            file_path: Path to the file to validate
            
        Returns:
            List of validation issues
        """
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Validate syntax
            syntax_issues = self.validate_syntax(content, file_path)
            issues.extend(syntax_issues)
            
            # Parse for deeper analysis
            tree = ast.parse(content)
            
            # Check imports
            for node in ast.walk(tree):
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    try:
                        # Verify import exists
                        exec(ast.unparse(node))
                    except ImportError as e:
                        issues.append(f"Invalid import at line {node.lineno}: {str(e)}")
                        
        except Exception as e:
            issues.append(f"Failed to validate file {file_path}: {str(e)}")
            
        return issues

class ValidationManager:
    """Manages code validation process."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.validator = CodeValidator(project_root)
        self.logger = logging.getLogger(__name__)
        
    def validate_update(self, file_paths: List[str]) -> Dict[str, List[str]]:
        """Validate a set of file updates.
        
        Args:
            file_paths: List of files to validate
            
        Returns:
            Dict mapping files to lists of validation issues
        """
        issues = {}
        
        for file_path in file_paths:
            file_issues = self.validator.validate_file(file_path)
            if file_issues:
                issues[file_path] = file_issues
                
        return issues
        
    def validate_class_updates(self, classes: List[Type]) -> Dict[str, List[str]]:
        """Validate updates to classes.
        
        Args:
            classes: List of classes to validate
            
        Returns:
            Dict mapping class names to lists of validation issues
        """
        issues = {}
        
        for cls in classes:
            class_issues = self.validator.validate_class(cls)
            if class_issues:
                issues[cls.__name__] = class_issues
                
        return issues