import os
import json
from datetime import datetime, timedelta

class CacheManager:
    def __init__(self, cache_dir="cache"):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        self.cache_duration = timedelta(hours=24)  # Cache for 24 hours

    def get_cache_key(self, api_method, params):
        return f"{api_method}_{hash(frozenset(params.items()))}.json"

    def get_cached_response(self, api_method, params):
        cache_key = self.get_cache_key(api_method, params)
        cache_path = os.path.join(self.cache_dir, cache_key)
        
        if os.path.exists(cache_path):
            with open(cache_path, 'r') as f:
                cache_data = json.load(f)
                timestamp = datetime.fromisoformat(cache_data['timestamp'])
                
                if datetime.now() - timestamp < self.cache_duration:
                    return cache_data['response']
        return None

    def cache_response(self, api_method, params, response):
        cache_key = self.get_cache_key(api_method, params)
        cache_path = os.path.join(self.cache_dir, cache_key)
        
        cache_data = {
            'timestamp': datetime.now().isoformat(),
            'response': response
        }
        
        with open(cache_path, 'w') as f:
            json.dump(cache_data, f)
