import unittest
import os
import tempfile
from tools.copilot_validator import CopilotRulesValidator, FeatureValidation

class TestValidator(unittest.TestCase):
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a temporary COPILOT_RULES.md
        rules_content = """
# Test Rules
## Test Section
```yaml
testing:
  required_methods:
    - __init__
    - validate_requirements
  documentation:
    required_sections:
      - Purpose
      - Parameters
```
"""
        with open(os.path.join(self.temp_dir, 'COPILOT_RULES.md'), 'w') as f:
            f.write(rules_content)
            
        self.validator = CopilotRulesValidator(self.temp_dir)

    def test_valid_feature(self):
        # Create a valid feature file
        valid_code = '''
class TestFeature:
    """
    Purpose:
        Test feature implementation
        
    Parameters:
        param1: str - Test parameter
    """
    
    def __init__(self):
        """Initialize the feature"""
        self.validate_requirements()
        
    def validate_requirements(self):
        """Validate all requirements"""
        try:
            self._check_dependencies()
        except Exception as e:
            self._handle_error(e)
    '''
        
        test_file = os.path.join(self.temp_dir, 'test_feature.py')
        with open(test_file, 'w') as f:
            f.write(valid_code)
            
        validation = self.validator.validate_feature(test_file)
        self.assertTrue(validation.requirements_met)
        self.assertTrue(validation.documentation_score > 0.8)

    def test_invalid_feature(self):
        # Create an invalid feature file
        invalid_code = '''
def incomplete_feature():
    # No documentation
    # No error handling
    pass
'''
        
        test_file = os.path.join(self.temp_dir, 'incomplete_feature.py')
        with open(test_file, 'w') as f:
            f.write(invalid_code)
            
        validation = self.validator.validate_feature(test_file)
        self.assertFalse(validation.requirements_met)
        self.assertTrue(validation.documentation_score < 0.2)

    def test_error_handling(self):
        # Test code with proper error handling
        code_with_errors = '''
class ErrorHandlingFeature:
    def process_data(self):
        try:
            self._do_processing()
        except ValueError as e:
            self._handle_value_error(e)
        except IOError as e:
            self._handle_io_error(e)
'''
        
        test_file = os.path.join(self.temp_dir, 'error_handling.py')
        with open(test_file, 'w') as f:
            f.write(code_with_errors)
            
        validation = self.validator.validate_feature(test_file)
        self.assertTrue(validation.error_handling_score > 0.5)

    def tearDown(self):
        # Clean up temporary files
        for root, _, files in os.walk(self.temp_dir):
            for file in files:
                os.remove(os.path.join(root, file))
        os.rmdir(self.temp_dir)

if __name__ == '__main__':
    unittest.main()
