"""
Settings Manager for Documentation Reader
Handles themes, font sizes, and user preferences
"""

from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QApplication
import json
import os

class SettingsManager(QObject):
    """Manages application settings and themes"""

    # Signals for settings changes
    theme_changed = pyqtSignal(str)
    font_size_changed = pyqtSignal(int)

    def __init__(self):
        super().__init__()
        self.settings_file = "doc_reader_settings.json"
        self.current_theme = "dark"
        self.explorer_font_size = 15
        self.content_font_size = 16
        self.load_settings()

    def load_settings(self):
        """Load settings from file"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r') as f:
                    settings = json.load(f)
                    self.current_theme = settings.get('theme', 'dark')
                    self.explorer_font_size = settings.get('explorer_font_size', 15)
                    self.content_font_size = settings.get('content_font_size', 16)
        except Exception as e:
            print(f"Error loading settings: {e}")

    def save_settings(self):
        """Save settings to file"""
        try:
            settings = {
                'theme': self.current_theme,
                'explorer_font_size': self.explorer_font_size,
                'content_font_size': self.content_font_size
            }
            with open(self.settings_file, 'w') as f:
                json.dump(settings, f, indent=2)
        except Exception as e:
            print(f"Error saving settings: {e}")

    def set_theme(self, theme_name):
        """Set application theme"""
        if theme_name in ['dark', 'light', 'auto']:
            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)
            self.save_settings()

    def set_explorer_font_size(self, size):
        """Set file explorer font size"""
        if 10 <= size <= 24:
            self.explorer_font_size = size
            self.font_size_changed.emit(size)
            self.save_settings()

    def set_content_font_size(self, size):
        """Set content viewer font size"""
        if 12 <= size <= 28:
            self.content_font_size = size
            self.save_settings()

    def get_dark_theme_stylesheet(self):
        """Get dark theme stylesheet"""
        font_size = str(self.explorer_font_size)
        return f"""
        QMainWindow {{
            background-color: #2b2b2b;
            color: #ffffff;
            selection-background-color: #3daee9;
        }}
        QTreeWidget {{
            background-color: #3c3c3c;
            border: 1px solid #555555;
            border-radius: 5px;
            font-size: {font_size}px;
            padding: 8px;
            alternate-background-color: #3c3c3c;
            outline: none;
        }}
        QTreeWidget::item {{
            padding: 12px 8px;
            border: none;
            color: #ffffff;
            font-size: {font_size}px;
            min-height: 28px;
            font-weight: 500;
            background-color: transparent;
        }}
        QTreeWidget::item:hover {{
            background-color: #4a4a4a;
            border-radius: 4px;
        }}
        QTreeWidget::item:selected {{
            background-color: #3daee9;
            border-radius: 4px;
            color: #ffffff;
        }}
        QTreeWidget::item:selected:hover {{
            background-color: #2980b9;
        }}
        QTextEdit {{
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #555555;
            border-radius: 5px;
        }}
        QSplitter {{
            background-color: #2b2b2b;
            border: none;
        }}
        QSplitter::handle {{
            background-color: #555555;
            width: 3px;
            margin: 2px;
            border-radius: 1px;
        }}
        QSplitter::handle:hover {{
            background-color: #3daee9;
        }}
        QLabel {{
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
        }}
        QPushButton {{
            background-color: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 5px;
            padding: 8px 16px;
            color: #ffffff;
            font-size: 13px;
            font-weight: bold;
            min-height: 25px;
            min-width: 80px;
        }}
        QPushButton:hover {{
            background-color: #5a5a5a;
            border-color: #3daee9;
        }}
        QPushButton:pressed {{
            background-color: #3a3a3a;
        }}
        QFrame {{
            background-color: #2b2b2b;
            border: 1px solid #555555;
            border-radius: 5px;
        }}
        QGroupBox {{
            font-weight: bold;
            border: 1px solid #555555;
            border-radius: 5px;
            margin-top: 8px;
            padding-top: 5px;
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }}
        QTabWidget::pane {{
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }}
        QTabBar::tab {{
            background-color: #4a4a4a;
            color: #ffffff;
            padding: 6px 12px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            min-height: 20px;
            max-height: 25px;
        }}
        QTabBar::tab:selected {{
            background-color: #3daee9;
        }}
        QTabBar::tab:hover {{
            background-color: #5a5a5a;
        }}
        QComboBox {{
            background-color: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 4px;
            padding: 6px;
            color: #ffffff;
            min-width: 100px;
        }}
        QComboBox:hover {{
            border-color: #3daee9;
        }}
        QComboBox::drop-down {{
            border: none;
        }}
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #ffffff;
        }}
        QSpinBox {{
            background-color: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 4px;
            padding: 6px;
            color: #ffffff;
            min-width: 60px;
        }}
        QSpinBox:hover {{
            border-color: #3daee9;
        }}
        """

    def get_light_theme_stylesheet(self):
        """Get light theme stylesheet"""
        font_size = str(self.explorer_font_size)
        return f"""
        QMainWindow {{
            background-color: #ffffff;
            color: #000000;
            selection-background-color: #3daee9;
        }}
        QTreeWidget {{
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: {font_size}px;
            padding: 8px;
            alternate-background-color: #f8f9fa;
            outline: none;
        }}
        QTreeWidget::item {{
            padding: 12px 8px;
            border: none;
            color: #000000;
            font-size: {font_size}px;
            min-height: 28px;
            font-weight: 500;
            background-color: transparent;
        }}
        QTreeWidget::item:hover {{
            background-color: #e9ecef;
            border-radius: 4px;
        }}
        QTreeWidget::item:selected {{
            background-color: #3daee9;
            border-radius: 4px;
            color: #ffffff;
        }}
        QTreeWidget::item:selected:hover {{
            background-color: #2980b9;
        }}
        QTextEdit {{
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }}
        QSplitter {{
            background-color: #ffffff;
            border: none;
        }}
        QSplitter::handle {{
            background-color: #dee2e6;
            width: 3px;
            margin: 2px;
            border-radius: 1px;
        }}
        QSplitter::handle:hover {{
            background-color: #3daee9;
        }}
        QLabel {{
            color: #000000;
            font-size: 14px;
            font-weight: 500;
        }}
        QPushButton {{
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 8px 16px;
            color: #000000;
            font-size: 13px;
            font-weight: bold;
            min-height: 25px;
            min-width: 80px;
        }}
        QPushButton:hover {{
            background-color: #e9ecef;
            border-color: #3daee9;
        }}
        QPushButton:pressed {{
            background-color: #dee2e6;
        }}
        QFrame {{
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }}
        QGroupBox {{
            font-weight: bold;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin-top: 8px;
            padding-top: 5px;
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }}
        QTabWidget::pane {{
            border: 1px solid #dee2e6;
            background-color: #ffffff;
        }}
        QTabBar::tab {{
            background-color: #f8f9fa;
            color: #000000;
            padding: 6px 12px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            min-height: 20px;
            max-height: 25px;
        }}
        QTabBar::tab:selected {{
            background-color: #3daee9;
            color: #ffffff;
        }}
        QTabBar::tab:hover {{
            background-color: #e9ecef;
        }}
        QComboBox {{
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 6px;
            color: #000000;
            min-width: 100px;
        }}
        QComboBox:hover {{
            border-color: #3daee9;
        }}
        QSpinBox {{
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 6px;
            color: #000000;
            min-width: 60px;
        }}
        QSpinBox:hover {{
            border-color: #3daee9;
        }}
        """
