{"timestamp": "2025-06-05T15:54:31.739061", "response": {"kind": "youtube#playlistItemListResponse", "etag": "8Ykc_2aars_uG6_WeWJDdfL1OuE", "nextPageToken": "EAAaHlBUOkNBb2lFRFEzTmtJd1JFTXlOVVEzUkVWRk9FRQ", "items": [{"kind": "youtube#playlistItem", "etag": "nfBShoOy406LQy87dKjrjvPc7AE", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy41NkI0NEY2RDEwNTU3Q0M2", "snippet": {"publishedAt": "2017-12-16T08:46:53Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "1 - Les bases de AutoCad Civil 3D 2018 - Introduction", "description": "Introduction du cours sur AutoCad Civil 3D 2018 l'excellent logiciel de conception VRD (Voirie, Réseaux Divers) et plus", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/yoNmgxZ3b-s/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/yoNmgxZ3b-s/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/yoNmgxZ3b-s/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/yoNmgxZ3b-s/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/yoNmgxZ3b-s/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 0, "resourceId": {"kind": "youtube#video", "videoId": "yoNmgxZ3b-s"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}, {"kind": "youtube#playlistItem", "etag": "1F_aeeYuyaFWVBQ_HdkVemD2uFA", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy4yODlGNEE0NkRGMEEzMEQy", "snippet": {"publishedAt": "2017-12-22T08:37:50Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "2 - Les bases de AutoCad Civil 3D 2018 - Les Points 1ère partie", "description": "Première partie du cours dédié aux points dans AutoCad Civil 3D 2018. Comme le dit le titre, vous apprendrez les bases (l'essentiel) des points dans ce excellent logiciel d'infrastructure d'Autodesk.\n\nSVP, n'oubliez pas de Liker, de Partager et de vous Abonnez pour m'encourager.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/ABrCYCgTNz4/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/ABrCYCgTNz4/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/ABrCYCgTNz4/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/ABrCYCgTNz4/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/ABrCYCgTNz4/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 1, "resourceId": {"kind": "youtube#video", "videoId": "ABrCYCgTNz4"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}, {"kind": "youtube#playlistItem", "etag": "-nClg7Ze0JdP27hkoAMuSHcpJt8", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy4wMTcyMDhGQUE4NTIzM0Y5", "snippet": {"publishedAt": "2017-12-26T12:11:42Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "3 - Les bases de AutoCad Civil 3D 2018   Les Points 2ème partie", "description": "Suite et fin du cours sur les points dans AutoCad Civil 3D 2018.\nDans cette vidéo, on parlera d'insertion de points et de quelques manipulations sur les points.\n\nLien fichier TN : https://drive.google.com/file/d/1XF0Tymi_qeuQhVhVcek4fr00nIB-TqVW/view?usp=sharing\n\nSVP, n'oubliez pas de Liker, de Partager, et de vous abonner pour m'encourager", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/IDiis_klPqE/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/IDiis_klPqE/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/IDiis_klPqE/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/IDiis_klPqE/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/IDiis_klPqE/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 2, "resourceId": {"kind": "youtube#video", "videoId": "IDiis_klPqE"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}, {"kind": "youtube#playlistItem", "etag": "KkxOUxrGCOxy05zjiOtPCjHLow8", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy41MjE1MkI0OTQ2QzJGNzNG", "snippet": {"publishedAt": "2017-12-30T16:32:32Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "4 - Les bases de AutoCad Civil 3D 2018 -  Les Surfaces", "description": "Dans cette vidéo, nous aborderons les base de Civil 3D pour ce est de la création et de la manipulation des surfaces", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/a57QqdtwryQ/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/a57QqdtwryQ/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/a57QqdtwryQ/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/a57QqdtwryQ/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/a57QqdtwryQ/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 3, "resourceId": {"kind": "youtube#video", "videoId": "a57QqdtwryQ"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}, {"kind": "youtube#playlistItem", "etag": "DcVsVucMzupJO83AH-LNgDMrw04", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy4wOTA3OTZBNzVEMTUzOTMy", "snippet": {"publishedAt": "2018-01-03T15:50:36Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "5 - Les bases de AutoCad Civil 3D 2018 - Les Axes 1", "description": "Dans cette vidéo, on aborde le le sujet de la création des axes.\nelle sera en partie revue après la vidéo sur les profils en long.\n\nLien fichier Axe : https://drive.google.com/file/d/12uHJIGeQ0fQySYCea8aUz47Wjpcjjv9l/view?usp=sharing", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/FEBwO28yWNQ/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/FEBwO28yWNQ/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/FEBwO28yWNQ/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/FEBwO28yWNQ/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/FEBwO28yWNQ/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 4, "resourceId": {"kind": "youtube#video", "videoId": "FEBwO28yWNQ"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}, {"kind": "youtube#playlistItem", "etag": "9FllT3PLOJ0S0G3OfDpTzJfLQw4", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy4xMkVGQjNCMUM1N0RFNEUx", "snippet": {"publishedAt": "2018-01-09T10:18:03Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "6 - Les bases de AutoCad Civil 3D 2018 - Tabulations", "description": "Cette vidéo montre la construction de tabulation dans AutoCad Civil 3D. On y montre aussi quelques manipulations de bases de ces tabulations.\n\nBon apprentissage !", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/fSxQT5cUGlE/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/fSxQT5cUGlE/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/fSxQT5cUGlE/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/fSxQT5cUGlE/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/fSxQT5cUGlE/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 5, "resourceId": {"kind": "youtube#video", "videoId": "fSxQT5cUGlE"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}, {"kind": "youtube#playlistItem", "etag": "0fuEYWKrkTE_MGpcbRiyQtF__5M", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy41MzJCQjBCNDIyRkJDN0VD", "snippet": {"publishedAt": "2018-01-16T09:09:45Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "7 - Les bases de AutoCad Civil 3D 2018 - Profils en Long", "description": "Dans cette 7ème vidéo, nous allons construire  les profils en long des deux tronçons.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/SkDJGSS_ggU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/SkDJGSS_ggU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/SkDJGSS_ggU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/SkDJGSS_ggU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/SkDJGSS_ggU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 6, "resourceId": {"kind": "youtube#video", "videoId": "SkDJGSS_ggU"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}, {"kind": "youtube#playlistItem", "etag": "EzvKyj3hySSwOWXU1SWJ33e6eR4", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy5DQUNERDQ2NkIzRUQxNTY1", "snippet": {"publishedAt": "2018-01-30T10:22:54Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "8 - Les bases de AutoCad Civil 3D 2018 - Raccourcis aux Données Partie 1", "description": "Les raccourcis au données sont un des  \"concepts\" importants dans C3D. <PERSON><PERSON>, je les utilise très souvent pour mettre, dans des fichiers qui leurs sont propres, les profils en travers de longs projets.\nDécouvrez les dans cette 1ére vidéo.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/EaEKk4KQLOg/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/EaEKk4KQLOg/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/EaEKk4KQLOg/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/EaEKk4KQLOg/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/EaEKk4KQLOg/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 7, "resourceId": {"kind": "youtube#video", "videoId": "EaEKk4KQLOg"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}, {"kind": "youtube#playlistItem", "etag": "ZsY5ZInh3TFnS2v9HHYIigGxy8s", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy45NDk1REZENzhEMzU5MDQz", "snippet": {"publishedAt": "2018-02-06T09:53:24Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "9 - Les bases de AutoCad Civil 3D 2018 - Réponse pour Question Profil en Long", "description": "Cette vidéo, la 9ème, est une réponse à une question sur \"l'habillage\" des profils en long.\nS'il y a des zones d'ombres ( un détail que vous ne comprenez pas ou qui manque), écrivez moi à l'adresse indiquée dans la vidéo ...", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/Y28Gr4-feac/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/Y28Gr4-feac/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/Y28Gr4-feac/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/Y28Gr4-feac/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/Y28Gr4-feac/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 8, "resourceId": {"kind": "youtube#video", "videoId": "Y28Gr4-feac"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}, {"kind": "youtube#playlistItem", "etag": "ImqHUSALm2nS9WTptCnJJ7Dh2y8", "id": "UExvdGt4Z1hDYmxWdjkxZmp0ZlRIVHpBRWkyRWRoZnRSUy5GNjNDRDREMDQxOThCMDQ2", "snippet": {"publishedAt": "2018-02-08T15:47:36Z", "channelId": "UCwTH1eJifTZ9FZjLp7dmfvA", "title": "10 - Les bases de AutoCad Civil 3D 2018 - Réponse pour Question Profil en Long 2", "description": "Cette 10ème vidéo  est la suite de la  réponse entamée dans la vidéo 9.\n<PERSON><PERSON>, on apprend comment ajouter soi-même, les ligne d'un jeu de bandes de données pour \"habiller\" le profil en long", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/_UlVhXV5mmk/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/_UlVhXV5mmk/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/_UlVhXV5mmk/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/_UlVhXV5mmk/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/_UlVhXV5mmk/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "ADSKMOD", "playlistId": "PLotkxgXCblVv91fjtfTHTzAEi2EdhftRS", "position": 9, "resourceId": {"kind": "youtube#video", "videoId": "_UlVhXV5mmk"}, "videoOwnerChannelTitle": "ADSKMOD", "videoOwnerChannelId": "UCwTH1eJifTZ9FZjLp7dmfvA"}}], "pageInfo": {"totalResults": 22, "resultsPerPage": 10}}}