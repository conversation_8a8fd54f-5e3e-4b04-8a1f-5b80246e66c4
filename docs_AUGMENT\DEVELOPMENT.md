# Development Guide - YouTube Explorer Application

## 🛠️ Development Environment Setup

### Prerequisites
- Python 3.8+ (recommended: 3.9 or 3.10)
- Git for version control
- Code editor (VS Code, PyCharm, etc.)
- YouTube Data API v3 key

### Initial Setup
```bash
# Clone the repository
git clone <repository-url>
cd youtube-explorer

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install development dependencies
pip install -r requirements-dev.txt  # If exists

# Set up pre-commit hooks
pre-commit install
```

### Environment Configuration
```bash
# Create .env file
cp .env.example .env

# Edit .env with your settings
API_KEY=your_youtube_api_key_here
LOG_LEVEL=DEBUG
CACHE_DURATION=3600
```

---

## 🏗️ Architecture Deep Dive

### Application Structure
```
youtube-explorer/
├── main.py                    # Application entry point
├── gui/
│   └── app.py                # Main GUI application class
├── logic/
│   └── youtube_api.py        # YouTube API wrapper
├── data/
│   ├── bookmark_manager.py   # Bookmark persistence
│   └── cache_manager.py      # API response caching
├── utils/
│   ├── logger.py            # Logging system
│   └── ui_helpers.py        # UI utility classes
└── tests/                   # Unit tests
```

### Design Patterns Used

#### 1. Model-View-Controller (MVC)
- **Model**: `data/` directory (BookmarkManager, CacheManager)
- **View**: `gui/app.py` (UI components)
- **Controller**: `logic/youtube_api.py` (Business logic)

#### 2. Singleton Pattern
```python
# Logger implementation
class AppLogger:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
```

#### 3. Observer Pattern
```python
# Event handling in UI
def on_bookmark_change(self, callback):
    self.bookmark_callbacks.append(callback)
```

#### 4. Strategy Pattern
```python
# Different search strategies
class SearchStrategy:
    def search(self, query): pass

class VideoSearchStrategy(SearchStrategy):
    def search(self, query): # Video-specific search

class PlaylistSearchStrategy(SearchStrategy):
    def search(self, query): # Playlist-specific search
```

---

## 🧪 Testing Strategy

### Test Structure
```
tests/
├── unit/
│   ├── test_youtube_api.py
│   ├── test_bookmark_manager.py
│   └── test_ui_helpers.py
├── integration/
│   ├── test_api_integration.py
│   └── test_ui_integration.py
└── fixtures/
    ├── sample_api_responses.json
    └── test_bookmarks.json
```

### Running Tests
```bash
# Run all tests
python -m pytest

# Run specific test file
python -m pytest tests/unit/test_youtube_api.py

# Run with coverage
python -m pytest --cov=. --cov-report=html

# Run integration tests (requires API key)
python -m pytest tests/integration/ -m integration
```

### Writing Tests

#### Unit Test Example
```python
import unittest
from unittest.mock import patch, MagicMock
from logic.youtube_api import YouTubeAPI

class TestYouTubeAPI(unittest.TestCase):
    
    def setUp(self):
        self.api = YouTubeAPI()
    
    @patch('requests.get')
    def test_search_videos_success(self, mock_get):
        # Mock API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "items": [{"snippet": {"title": "Test Video"}}]
        }
        mock_get.return_value = mock_response
        
        # Test the method
        result = self.api.search_videos("test query")
        
        # Assertions
        self.assertIsNotNone(result)
        self.assertEqual(len(result["items"]), 1)
        self.assertEqual(result["items"][0]["snippet"]["title"], "Test Video")
```

#### Integration Test Example
```python
import pytest
from gui.app import YouTubeExplorerApp

@pytest.mark.integration
class TestAppIntegration:
    
    def test_full_search_workflow(self):
        app = YouTubeExplorerApp()
        
        # Simulate search
        app.search_entry.insert(0, "Python tutorial")
        app.perform_search()
        
        # Verify results
        assert len(app.video_tree.get_children()) > 0
```

---

## 🎨 UI Development Guidelines

### Tkinter Best Practices

#### 1. Widget Organization
```python
def create_widgets(self):
    # Group related widgets in frames
    self.search_frame = ttk.Frame(self.root)
    self.results_frame = ttk.Frame(self.root)
    self.bookmark_frame = ttk.Frame(self.root)
    
    # Use consistent padding
    for frame in [self.search_frame, self.results_frame, self.bookmark_frame]:
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
```

#### 2. Event Handling
```python
def bind_events(self):
    # Use lambda for simple callbacks
    self.search_button.config(command=lambda: self.perform_search())
    
    # Use methods for complex callbacks
    self.video_tree.bind('<Double-1>', self.on_video_double_click)
    self.video_tree.bind('<Button-3>', self.show_context_menu)
```

#### 3. Responsive Design
```python
def configure_layout(self):
    # Use weight for responsive columns
    self.root.columnconfigure(0, weight=1)
    self.root.columnconfigure(1, weight=2)
    self.root.rowconfigure(0, weight=1)
    
    # Configure treeview columns
    for col in self.video_tree["columns"]:
        self.video_tree.column(col, width=100, minwidth=50)
```

### UI Component Guidelines

#### Custom Widgets
```python
class SearchWidget(ttk.Frame):
    def __init__(self, parent, search_callback):
        super().__init__(parent)
        self.search_callback = search_callback
        self.create_widgets()
    
    def create_widgets(self):
        self.entry = ttk.Entry(self, width=50)
        self.button = ttk.Button(self, text="Search", command=self.on_search)
        
        self.entry.pack(side=tk.LEFT, padx=(0, 10))
        self.button.pack(side=tk.LEFT)
    
    def on_search(self):
        query = self.entry.get()
        if query:
            self.search_callback(query)
```

#### Error Handling in UI
```python
def safe_ui_operation(self, operation, *args, **kwargs):
    try:
        return operation(*args, **kwargs)
    except Exception as e:
        self.error_manager.show_error("UI Error", str(e))
        self.logger.error(f"UI operation failed: {e}")
```

---

## 🔧 Code Quality Standards

### Code Style
- Follow PEP 8 guidelines
- Use type hints where possible
- Maximum line length: 88 characters (Black formatter)
- Use meaningful variable and function names

#### Example with Type Hints
```python
from typing import Dict, List, Optional, Union

def search_videos(self, query: str, max_results: int = 10) -> Optional[Dict]:
    """Search for YouTube videos.
    
    Args:
        query: Search query string
        max_results: Maximum number of results to return
        
    Returns:
        Dictionary containing search results or None if failed
    """
    if not query.strip():
        return None
    
    # Implementation here
    pass
```

### Documentation Standards
```python
class YouTubeAPI:
    """YouTube Data API wrapper.
    
    This class provides a simplified interface to the YouTube Data API v3,
    including caching and error handling capabilities.
    
    Attributes:
        api_key: YouTube API key from environment variables
        cache_manager: Instance for caching API responses
        
    Example:
        >>> api = YouTubeAPI()
        >>> results = api.search_videos("Python tutorials")
        >>> print(len(results["items"]))
        10
    """
```

### Error Handling Patterns
```python
def robust_api_call(self, operation: str, **kwargs) -> Optional[Dict]:
    """Make a robust API call with retry logic."""
    max_retries = 3
    base_delay = 1
    
    for attempt in range(max_retries):
        try:
            return self._make_api_call(operation, **kwargs)
        except requests.exceptions.Timeout:
            if attempt == max_retries - 1:
                raise
            time.sleep(base_delay * (2 ** attempt))
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API call failed: {e}")
            if attempt == max_retries - 1:
                raise
            time.sleep(base_delay)
    
    return None
```

---

## 🚀 Performance Optimization

### Profiling Tools
```python
# Performance profiling
import cProfile
import pstats
from functools import wraps

def profile_function(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        pr = cProfile.Profile()
        pr.enable()
        result = func(*args, **kwargs)
        pr.disable()
        
        stats = pstats.Stats(pr)
        stats.sort_stats('cumulative')
        stats.print_stats(10)
        
        return result
    return wrapper

# Usage
@profile_function
def perform_search(self):
    # Search implementation
    pass
```

### Memory Optimization
```python
# Use generators for large datasets
def get_all_bookmarks(self):
    for bookmark_id, data in self.bookmarks.items():
        yield bookmark_id, data

# Implement cache size limits
class LimitedCache:
    def __init__(self, max_size=100):
        self.cache = {}
        self.max_size = max_size
    
    def set(self, key, value):
        if len(self.cache) >= self.max_size:
            # Remove oldest entry
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        self.cache[key] = value
```

### UI Performance
```python
# Lazy loading for large lists
def populate_tree_lazy(self, items):
    # Only load visible items initially
    visible_count = 20
    
    for i, item in enumerate(items[:visible_count]):
        self.tree.insert("", tk.END, values=item)
    
    # Load remaining items in background
    if len(items) > visible_count:
        self.root.after(100, lambda: self.load_remaining_items(items[visible_count:]))
```

---

## 🔄 Continuous Integration

### GitHub Actions Workflow
```yaml
name: CI/CD Pipeline

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10']
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run linting
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        pylint gui/ logic/ data/ utils/
    
    - name: Run tests
      run: |
        pytest --cov=. --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

### Pre-commit Configuration
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3
  
  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
  
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
```

---

## 📦 Release Process

### Version Management
```python
# version.py
__version__ = "1.0.0"
__author__ = "YouTube Explorer Team"
__email__ = "<EMAIL>"

# Semantic versioning: MAJOR.MINOR.PATCH
# MAJOR: Breaking changes
# MINOR: New features, backward compatible
# PATCH: Bug fixes, backward compatible
```

### Build and Distribution
```bash
# Create distribution packages
python setup.py sdist bdist_wheel

# Upload to PyPI (test)
twine upload --repository-url https://test.pypi.org/legacy/ dist/*

# Upload to PyPI (production)
twine upload dist/*
```

---

*Development guide last updated: June 2025*
