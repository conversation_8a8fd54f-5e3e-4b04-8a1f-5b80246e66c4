"""Base component template for consistent UI implementation."""

import tkinter as tk
from tkinter import ttk
from typing import Any, Callable, Dict, Optional
import logging

class BaseComponent:
    """Base class for UI components."""
    
    def __init__(self, parent: tk.Widget):
        """Initialize base component.
        
        Args:
            parent: Parent widget
        """
        self.parent = parent
        self.logger = logging.getLogger(self.__class__.__name__)
        self.frame = ttk.Frame(parent)
        self._setup_component()
    
    def _setup_component(self):
        """Set up the component."""
        self._init_variables()
        self._create_widgets()
        self._layout_widgets()
        self._bind_events()
        self._load_initial_state()
    
    def _init_variables(self):
        """Initialize component variables."""
        self.variables: Dict[str, tk.Variable] = {}
    
    def _create_widgets(self):
        """Create component widgets."""
        pass
    
    def _layout_widgets(self):
        """Layout component widgets."""
        self.frame.grid(sticky='nsew')
    
    def _bind_events(self):
        """Bind component events."""
        pass
    
    def _load_initial_state(self):
        """Load initial component state."""
        pass
    
    def handle_error(self, error_type: str, detail: str, 
                    handler: Optional[Callable[[str, str], None]] = None) -> None:
        """Handle component errors.
        
        Args:
            error_type: Type of error
            detail: Error details
            handler: Optional custom error handler
        """
        log_entry = {
            'component': self.__class__.__name__,
            'error_type': error_type,
            'detail': detail
        }
        self.logger.error(f"Component error: {log_entry}")
        
        if handler:
            handler(error_type, detail)
        else:
            self._default_error_handler(error_type, detail)
    
    def _default_error_handler(self, error_type: str, detail: str):
        """Default error handler implementation."""
        # Implement default error handling
        pass
    
    def update_state(self, state: Dict[str, Any]):
        """Update component state.
        
        Args:
            state: New state values
        """
        try:
            for key, value in state.items():
                if key in self.variables:
                    self.variables[key].set(value)
        except Exception as e:
            self.handle_error('state_update_failed', str(e))
    
    def get_state(self) -> Dict[str, Any]:
        """Get current component state.
        
        Returns:
            Dict containing current state
        """
        return {key: var.get() for key, var in self.variables.items()}
    
    def destroy(self):
        """Clean up component resources."""
        try:
            self.frame.destroy()
        except Exception as e:
            self.logger.error(f"Error destroying component: {e}")

class ExampleComponent(BaseComponent):
    """Example implementation of BaseComponent."""
    
    def _init_variables(self):
        """Initialize example component variables."""
        super()._init_variables()
        self.variables['counter'] = tk.IntVar(value=0)
    
    def _create_widgets(self):
        """Create example component widgets."""
        self.label = ttk.Label(
            self.frame, 
            textvariable=self.variables['counter']
        )
        self.button = ttk.Button(
            self.frame,
            text="Increment",
            command=self._on_button_click
        )
    
    def _layout_widgets(self):
        """Layout example component widgets."""
        super()._layout_widgets()
        self.label.grid(row=0, column=0, padx=5, pady=5)
        self.button.grid(row=0, column=1, padx=5, pady=5)
    
    def _on_button_click(self):
        """Handle button click event."""
        try:
            current = self.variables['counter'].get()
            self.variables['counter'].set(current + 1)
        except Exception as e:
            self.handle_error('click_failed', str(e))
