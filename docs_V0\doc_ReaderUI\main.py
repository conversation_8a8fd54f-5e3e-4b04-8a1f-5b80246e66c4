#!/usr/bin/env python3
"""
YouTube Explorer Documentation Reader
A beautiful Qt-based dashboard for exploring project documentation
"""

import sys
import os
from pathlib import Path
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from doc_reader_app import DocumentationReaderApp

def main():
    """Main entry point for the Documentation Reader application"""
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("YouTube Explorer Documentation Reader")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("YouTube Explorer Team")
    
    # Set application style
    app.setStyle('Fusion')
    
    # Set global font
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # Apply dark theme
    apply_dark_theme(app)
    
    # Determine docs root directory (parent of doc_ReaderUI)
    docs_root = current_dir.parent
    
    # Create and show main window
    window = DocumentationReaderApp(docs_root)
    window.show()
    
    # Center window on screen
    window.center_on_screen()
    
    # Start event loop
    sys.exit(app.exec_())

def apply_dark_theme(app):
    """Apply a modern dark theme to the application"""
    
    dark_stylesheet = """
    QMainWindow {
        background-color: #2b2b2b;
        color: #ffffff;
    }
    
    QWidget {
        background-color: #2b2b2b;
        color: #ffffff;
        selection-background-color: #3daee9;
        selection-color: #ffffff;
    }
    
    QTreeWidget {
        background-color: #3c3c3c;
        border: 1px solid #555555;
        border-radius: 5px;
        padding: 5px;
        outline: none;
    }
    
    QTreeWidget::item {
        padding: 8px;
        border-bottom: 1px solid #555555;
        color: #ffffff;
    }
    
    QTreeWidget::item:selected {
        background-color: #3daee9;
        color: #ffffff;
    }
    
    QTreeWidget::item:hover {
        background-color: #4a4a4a;
    }
    
    QTextEdit {
        background-color: #ffffff;
        border: 1px solid #555555;
        border-radius: 5px;
        padding: 10px;
        color: #000000;
        font-family: 'Segoe UI', Arial, sans-serif;
        font-size: 11pt;
        line-height: 1.6;
    }
    
    QSplitter::handle {
        background-color: #555555;
        width: 3px;
        height: 3px;
    }
    
    QSplitter::handle:hover {
        background-color: #3daee9;
    }
    
    QLabel {
        color: #ffffff;
        font-weight: bold;
    }
    
    QStatusBar {
        background-color: #3c3c3c;
        border-top: 1px solid #555555;
        color: #ffffff;
    }
    
    QMenuBar {
        background-color: #3c3c3c;
        border-bottom: 1px solid #555555;
        color: #ffffff;
    }
    
    QMenuBar::item {
        background-color: transparent;
        padding: 8px 12px;
    }
    
    QMenuBar::item:selected {
        background-color: #3daee9;
    }
    
    QMenu {
        background-color: #3c3c3c;
        border: 1px solid #555555;
        color: #ffffff;
    }
    
    QMenu::item {
        padding: 8px 20px;
    }
    
    QMenu::item:selected {
        background-color: #3daee9;
    }
    
    QToolBar {
        background-color: #3c3c3c;
        border: none;
        spacing: 3px;
        padding: 5px;
    }
    
    QPushButton {
        background-color: #4a4a4a;
        border: 1px solid #666666;
        border-radius: 5px;
        padding: 8px 16px;
        color: #ffffff;
        font-weight: bold;
    }
    
    QPushButton:hover {
        background-color: #5a5a5a;
        border-color: #3daee9;
    }
    
    QPushButton:pressed {
        background-color: #3daee9;
    }
    
    QScrollBar:vertical {
        background-color: #3c3c3c;
        width: 12px;
        border-radius: 6px;
    }
    
    QScrollBar::handle:vertical {
        background-color: #666666;
        border-radius: 6px;
        min-height: 20px;
    }
    
    QScrollBar::handle:vertical:hover {
        background-color: #777777;
    }
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        height: 0px;
    }
    
    QScrollBar:horizontal {
        background-color: #3c3c3c;
        height: 12px;
        border-radius: 6px;
    }
    
    QScrollBar::handle:horizontal {
        background-color: #666666;
        border-radius: 6px;
        min-width: 20px;
    }
    
    QScrollBar::handle:horizontal:hover {
        background-color: #777777;
    }
    
    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
        width: 0px;
    }
    """
    
    app.setStyleSheet(dark_stylesheet)

if __name__ == "__main__":
    main()
