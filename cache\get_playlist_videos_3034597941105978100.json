{"timestamp": "2025-06-04T19:03:05.686327", "response": {"kind": "youtube#playlistItemListResponse", "etag": "m1EVl0nMcSJcek46ALRgJrApXO8", "nextPageToken": "EAAaHlBUOkNBb2lFRVF3UVRCRlJqa3pSRU5GTlRjME1rSQ", "items": [{"kind": "youtube#playlistItem", "etag": "PBpSE6rZWJDR3a-JQLEnTBLyq64", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS4yODlGNEE0NkRGMEEzMEQy", "snippet": {"publishedAt": "2023-03-16T13:48:30Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. The use of Forms in Aveva E3D. Lesson 1", "description": "This video covers:\n- Docking the Forms\n- Hiding the Forms\n- Splitting the Sliding Form\n- Arranging the Forms in the Sliding Form Sets\n\n#aveva #pdms #e3d", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/f21nNWzkwcA/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/f21nNWzkwcA/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/f21nNWzkwcA/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/f21nNWzkwcA/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/f21nNWzkwcA/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 0, "resourceId": {"kind": "youtube#video", "videoId": "f21nNWzkwcA"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}, {"kind": "youtube#playlistItem", "etag": "DiIQGhJSfjVB-yChwQP6R_i9l84", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS4wMTcyMDhGQUE4NTIzM0Y5", "snippet": {"publishedAt": "2023-03-16T15:23:00Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. The PowerWheel function. Lesson 2", "description": "The PowerWhee is a tool within the Model operations that can be accessed by right-clicking in the 3D View. It allows for the control and selection of functions through left-clicking on the required tile or indicating it by moving the cursor in the appropriate direction.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/zRVAxkecwKk/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/zRVAxkecwKk/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/zRVAxkecwKk/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/zRVAxkecwKk/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/zRVAxkecwKk/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 1, "resourceId": {"kind": "youtube#video", "videoId": "zRVAxkecwKk"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}, {"kind": "youtube#playlistItem", "etag": "aogl5lLtZRxDEwotuFompHfLClY", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS41MjE1MkI0OTQ2QzJGNzNG", "snippet": {"publishedAt": "2023-03-17T13:16:36Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. Using Canvas Commands. Lesson 3", "description": "Here is the list of some useful Function Keys with a Description\nF1 Aveva Help\nF2 Clear 3D View\nF3 Object Snap On/Off\nF4 Object Snap Projection On/Off\nF5 Toggle Walk Mode On/Off\nF6 Toggle Fly Mode On/Off\nF7 Grid On/Off\nF8 Orthogonal drawing On/Off\nF9 Grid Snap On/Off\nF10 Polar Tracking On/Off\nF11 Toggle Shaded/Wireline mode\nF12 Dynamic hints On/Off\n\nGeneral  Keys\nDelete - Deletes selected elements in 3D View with confirmation\nTab - Navigates to Contextual Editor input boxes\nSpace - Locks value in the Contextual Editor input boxes\nHome or Page Up - Navigates to WORL* level in any Explorer\nEnd or Page Down - Navigates to Bottom Level in any Explorer\nEsc - Exits current CIE Operation/cancels position input/ removes selection/ exits edit mode\nArrow UP - Shows In-3D view command of last used CIE Operation\nArrow Down - Shows available Tile Icons during CIE Operation\nInsert - Toggle Feature Highlight function On/Off\nBackspace -Removes selection from 3D view\nEnter - Confirms position, selects default option, executes the command, etc.\n\nFor other commands see this link - shorturl.at/joJSX", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/CpvFn4FwUWE/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/CpvFn4FwUWE/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/CpvFn4FwUWE/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/CpvFn4FwUWE/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/CpvFn4FwUWE/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 2, "resourceId": {"kind": "youtube#video", "videoId": "CpvFn4FwUWE"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}, {"kind": "youtube#playlistItem", "etag": "W9FJTQMWm9jKPiYpRlhtBspmu5k", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS4wOTA3OTZBNzVEMTUzOTMy", "snippet": {"publishedAt": "2023-03-17T14:39:54Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. Contextual Editor. Lesson 4", "description": "This lesson explains how to use certain commands in AVEVA Everything3D, such as the MOVE command, which requires user input. The user can enter values or select options directly in the 3D View using the \"Contextual Editor\" prompt. The default option is the current option, but alternative options can be selected by pressing the down-arrow key and clicking on the appropriate tile or pressing the corresponding key. Navigation between text boxes is achieved by pressing the tab key. The video also explains how to switch between Absolute and Relative modes and between Cartesian, cylindrical, or polar coordinates.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/4p84b_w5TvM/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/4p84b_w5TvM/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/4p84b_w5TvM/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/4p84b_w5TvM/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/4p84b_w5TvM/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 3, "resourceId": {"kind": "youtube#video", "videoId": "4p84b_w5TvM"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}, {"kind": "youtube#playlistItem", "etag": "ZQHGl6qjytXmO9lDU45E1CjhCpM", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS4xMkVGQjNCMUM1N0RFNEUx", "snippet": {"publishedAt": "2023-03-17T15:09:50Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. Model Explorer. Lesson 5", "description": "This video covers how to use Model Explorer.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/x0VxRHji2rY/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/x0VxRHji2rY/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/x0VxRHji2rY/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/x0VxRHji2rY/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/x0VxRHji2rY/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 4, "resourceId": {"kind": "youtube#video", "videoId": "x0VxRHji2rY"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}, {"kind": "youtube#playlistItem", "etag": "ugG3EH8Q9hh2zvmKFMz4FbuDTeU", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS41MzJCQjBCNDIyRkJDN0VD", "snippet": {"publishedAt": "2023-03-17T15:53:36Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. Using of 3D view. Lesson 6", "description": "This video covers: \n- Selecting the Elements to Display in the 3D View \n- Adding Elements to the 3D View \n- Adding other elements to the 3D View \n-  Removing Elements from the 3D View and The Drawlist form.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/pRS8_W4ftSo/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/pRS8_W4ftSo/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/pRS8_W4ftSo/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/pRS8_W4ftSo/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/pRS8_W4ftSo/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 5, "resourceId": {"kind": "youtube#video", "videoId": "pRS8_W4ftSo"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}, {"kind": "youtube#playlistItem", "etag": "dbKgCX0SD553mhXRll-hbC0B_tc", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS5DQUNERDQ2NkIzRUQxNTY1", "snippet": {"publishedAt": "2023-03-17T16:16:35Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. Controlling of 3d view. Lesson 7", "description": "This video covers: \n1. Controlling the 3D View\n2. View Limits\n3. Zooming\n4. Rotating the 3D View\n5. View Direction\n6. Panning\n7. Centre View", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/gWUlYjRAbvA/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/gWUlYjRAbvA/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/gWUlYjRAbvA/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/gWUlYjRAbvA/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/gWUlYjRAbvA/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 6, "resourceId": {"kind": "youtube#video", "videoId": "gWUlYjRAbvA"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}, {"kind": "youtube#playlistItem", "etag": "_AJB2ZerZZVNUCdzQgqp44SV2I0", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS45NDk1REZENzhEMzU5MDQz", "snippet": {"publishedAt": "2023-03-19T16:02:19Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. Clipping utility. Lesson 8", "description": "In this video, you will see how the Clipping tool can be utilized to conceal sections of a model that exceed a volume defined by the user.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/gz_Greu4ng0/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/gz_Greu4ng0/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/gz_Greu4ng0/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/gz_Greu4ng0/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/gz_Greu4ng0/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 7, "resourceId": {"kind": "youtube#video", "videoId": "gz_Greu4ng0"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}, {"kind": "youtube#playlistItem", "etag": "RhZASKPd1aA9SN9fdrp_biLX_2I", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS5GNjNDRDREMDQxOThCMDQ2", "snippet": {"publishedAt": "2023-03-19T16:20:07Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. Changing the Owner of an element. Lesson 9", "description": "In this video, you will learn how to adjust the position of an element within a hierarchy by modifying its owner. The tutorial will guide you through the process step-by-step.", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/6iWNFQZOI1k/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/6iWNFQZOI1k/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/6iWNFQZOI1k/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/6iWNFQZOI1k/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/6iWNFQZOI1k/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 8, "resourceId": {"kind": "youtube#video", "videoId": "6iWNFQZOI1k"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}, {"kind": "youtube#playlistItem", "etag": "w2-OsmosJkv6-KtUByC-t6CzIYI", "id": "UEwxalRZd2JzSXFWb0Vlb3JxOVotT05rb0RRdlM2Tlk0TS40NzZCMERDMjVEN0RFRThB", "snippet": {"publishedAt": "2023-03-19T16:39:56Z", "channelId": "UCKoguGphFGu9zv3jXz0SiEQ", "title": "Aveva E3D. Reordering the Hierarchy. Lesson 10", "description": "In this video tutorial, you will discover how to reposition an element within a hierarchy by altering its order. The process will be clearly explained and demonstrated to help you understand the steps involved.\n\n#Aveva #e3d", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/XXlW2f40ORU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/XXlW2f40ORU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/XXlW2f40ORU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/XXlW2f40ORU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/XXlW2f40ORU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "SmartProject", "playlistId": "PL1jTYwbsIqVoEeorq9Z-ONkoDQvS6NY4M", "position": 9, "resourceId": {"kind": "youtube#video", "videoId": "XXlW2f40ORU"}, "videoOwnerChannelTitle": "SmartProject", "videoOwnerChannelId": "UCKoguGphFGu9zv3jXz0SiEQ"}}], "pageInfo": {"totalResults": 20, "resultsPerPage": 10}}}