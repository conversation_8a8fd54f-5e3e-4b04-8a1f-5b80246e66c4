# Tree Component Patterns

## Direct ID Storage Pattern
Store IDs directly in tree tags for efficient lookup and navigation:

```python
def insert_item(self, item_id: str, values: list):
    """Insert item with direct ID storage in tag."""
    self.tree.insert('', 'end', tags=(item_id,), values=values)

def get_selected_id(self) -> str:
    """Get ID directly from selected item's tag."""
    selection = self.tree.selection()
    if selection:
        return self.tree.item(selection[0])['tags'][0]
    return None
```

## Column Configuration Pattern
Define columns with proper alignment and width:

```python
def configure_columns(self):
    """Configure treeview columns with proper alignment and sizing."""
    for col, width in zip(self.columns, self.column_widths):
        self.tree.column(col, width=width, anchor='w')
        self.tree.heading(col, text=col, anchor='w')
```

## Event Handler Pattern
Implement event handlers with error protection:

```python
def bind_handlers(self):
    """Bind tree event handlers with error protection."""
    self.tree.bind('<Double-1>', lambda e: self._safe_handler(self._on_double_click, e))
    self.tree.bind('<<TreeviewSelect>>', lambda e: self._safe_handler(self._on_select, e))

def _safe_handler(self, handler, event):
    """Wrap event handlers with error protection."""
    try:
        handler(event)
    except Exception as e:
        self.error_manager.show_error('tree_action_failed', str(e))
```
