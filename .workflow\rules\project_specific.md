# YouTube Explorer Project Conventions

## UI Components
- Use TreeviewPanel for list displays
- Implement ControlPanel for media controls
- Keep consistent spacing and alignment
- Follow system theme guidelines

## Data Management
- Store bookmarks with direct ID references
- Use JSON for configuration storage
- Implement automatic data backup
- Cache frequently accessed data

## Error Handling
- Display user-friendly error dialogs
- Log errors with context
- Implement automatic recovery
- Preserve user data during errors

## Testing Requirements
- Unit test all utility functions
- Test bookmark operations
- Verify error recovery
- Test UI responsiveness

## Documentation
- Document keyboard shortcuts
- Maintain feature documentation
- Update setup instructions
- Include troubleshooting guide
