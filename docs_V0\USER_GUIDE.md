# User Guide - YouTube Explorer V0

## 🎯 Getting Started

Welcome to YouTube Explorer! This comprehensive guide will help you master all features of the application.

## 📥 Installation & Setup

### Prerequisites
- Python 3.8 or higher
- Internet connection
- YouTube Data API v3 key

### Quick Setup
1. **Install Python dependencies:**
   ```bash
   cd app_rebuild_AUGMENT
   pip install -r requirements.txt
   ```

2. **Configure API key:**
   - Open `.env` file
   - Add your YouTube API key: `API_KEY=your_api_key_here`

3. **Launch the application:**
   ```bash
   python main.py
   ```

### Getting a YouTube API Key
1. Visit [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable YouTube Data API v3
4. Create credentials (API Key)
5. Copy the key to your `.env` file

## 🖥️ Interface Overview

### Main Window Layout
```
┌─────────────────────────────────────────────────────────┐
│ Search Bar [Videos] [Playlists] [Both] [Search Button] │
├─────────────────────┬─────────────────────┬─────────────┤
│     Video Panel     │   Playlist Panel    │  Bookmarks  │
│                     │                     │             │
│ • Title             │ • Title             │ Collections │
│ • Channel           │ • Channel           │ ┌─────────┐ │
│ • Views             │ • Video Count       │ │ All ▼   │ │
│ • Upload Date       │ • Bookmark Status   │ └─────────┘ │
│ • Bookmark Status   │                     │             │
│                     │                     │ Bookmarks   │
│                     │                     │ • Title     │
│                     │                     │ • Type      │
│                     │                     │ • Date      │
│                     │                     │ • Notes     │
└─────────────────────┴─────────────────────┴─────────────┘
```

### Key Interface Elements
- **Search Bar**: Enter keywords to find content
- **Mode Buttons**: Choose Videos, Playlists, or Both
- **Video Panel**: Displays video search results
- **Playlist Panel**: Shows playlist search results
- **Bookmark Panel**: Manages your saved content

## 🔍 Searching for Content

### Basic Search
1. **Enter search terms** in the search bar
2. **Select search mode**:
   - **Videos**: Search for individual videos
   - **Playlists**: Search for playlists
   - **Both**: Search for videos and playlists simultaneously
3. **Click Search** or press Enter
4. **View results** in the appropriate panels

### Search Tips
- **Use specific keywords** for better results
- **Try different search modes** to find varied content
- **Combine terms** like "Python tutorial beginner"
- **Use quotes** for exact phrases: "machine learning basics"

### Understanding Results
- **Video Results**: Title, channel, view count, upload date
- **Playlist Results**: Title, channel, video count
- **Bookmark Status**: ★ (bookmarked) or ☆ (not bookmarked)

## 📚 Managing Bookmarks

### Adding Bookmarks
1. **Search for content** you want to bookmark
2. **Click the star symbol** (☆) next to any item
3. **Star turns solid** (★) indicating it's bookmarked
4. **Item appears** in the Bookmarks panel

### Removing Bookmarks
**Method 1: From Search Results**
- Click the solid star (★) to remove bookmark

**Method 2: From Bookmark Panel**
- Select bookmark(s) in the Bookmarks panel
- Press Delete key or use context menu

**Method 3: Using Toolbar**
- Select bookmark(s)
- Click "🗑️ Remove Selected" button

### Organizing with Collections

#### Creating Collections
1. **Click "➕ New Collection"** in the Bookmarks panel
2. **Enter collection name** in the dialog
3. **Click Create** to save

#### Moving Bookmarks to Collections
**Method 1: During Creation**
- Select bookmarks before creating collection
- They'll automatically move to the new collection

**Method 2: Drag and Drop**
- Drag bookmarks between collections
- Drop on target collection items

**Method 3: Context Menu**
- Right-click bookmark
- Select "Move to Collection"
- Choose target collection

#### Managing Collections
- **View Collection**: Select from dropdown to filter bookmarks
- **Delete Collection**: Click 🗑️ next to collection dropdown
- **Rename Collection**: Currently requires manual editing

### Adding Notes to Bookmarks
1. **Click on the Notes column** for any bookmark
2. **Notes editor dialog** opens
3. **Type your notes** in the text area
4. **Click Save** to store notes
5. **Notes appear** in the Notes column

### Bookmark Features
- **Persistent Storage**: Bookmarks saved automatically
- **Rich Metadata**: Title, type, timestamp, notes
- **Quick Access**: Double-click to open in browser
- **Organization**: Collections for grouping
- **Search Integration**: Bookmark status shown in search results

## 🎮 Keyboard Shortcuts

### Global Shortcuts
- **Enter**: Execute search (when in search bar)
- **Delete**: Remove selected items
- **B**: Toggle bookmark for selected item
- **Escape**: Close dialogs

### Navigation
- **Tab**: Move between interface elements
- **Arrow Keys**: Navigate within lists
- **Space**: Select/deselect items
- **Ctrl+A**: Select all (in focused panel)

### Context Menus
- **Right-click**: Open context menu for additional options
- **Context menus available** for all panels

## 🎯 Advanced Features

### Playlist Exploration
1. **Search for playlists** using Playlist mode
2. **Click on any playlist** in results
3. **Videos from playlist** appear in Video panel
4. **Bookmark individual videos** or the entire playlist

### Batch Operations
- **Select multiple items**: Ctrl+click or Shift+click
- **Bulk bookmark**: Select multiple items, press B
- **Bulk delete**: Select multiple items, press Delete
- **Collection operations**: Move multiple bookmarks at once

### Visual Feedback
- **Tooltips**: Hover over elements for help
- **Status indicators**: Visual feedback for actions
- **Animations**: Smooth transitions and highlights
- **Progress feedback**: Loading indicators for operations

## 🔧 Customization & Settings

### Display Options
- **Symbol fallbacks**: Automatic fallback for unsupported symbols
- **Responsive layout**: Interface adapts to window size
- **Column sizing**: Adjust column widths by dragging

### Performance Settings
- **Cache duration**: 24-hour default (configurable in code)
- **Search limits**: 10 results default (adjustable)
- **Memory management**: Automatic cleanup

## 🚨 Troubleshooting

### Common Issues

#### "API_KEY not set" Error
**Solution:**
1. Check `.env` file exists in application directory
2. Verify API key is correctly formatted: `API_KEY=your_key_here`
3. Ensure no extra spaces or quotes around the key

#### No Search Results
**Possible Causes:**
- **Network connection**: Check internet connectivity
- **API quota**: Daily limit may be exceeded
- **Search terms**: Try different keywords

**Solutions:**
1. Check network connection
2. Try different search terms
3. Wait if quota exceeded (resets daily)

#### Bookmarks Not Saving
**Possible Causes:**
- **File permissions**: Cannot write to directory
- **Disk space**: Insufficient storage space

**Solutions:**
1. Check file permissions in application directory
2. Ensure sufficient disk space
3. Run application with appropriate permissions

#### Symbols Not Displaying
**Symptoms:** Stars appear as squares or question marks
**Solution:** Application automatically uses fallback symbols (*, o, X)

### Performance Issues

#### Slow Search Response
**Solutions:**
1. Check internet connection speed
2. Clear cache directory
3. Reduce max_results in search

#### High Memory Usage
**Solutions:**
1. Restart application periodically
2. Clear old bookmarks
3. Limit concurrent operations

### Getting Help
1. **Check error logs**: `youtube_explorer.log` file
2. **Review documentation**: This guide and API docs
3. **Check configuration**: Verify `.env` settings

## 💡 Tips & Best Practices

### Efficient Searching
- **Use specific terms** for better results
- **Try multiple search modes** for comprehensive results
- **Bookmark interesting content** immediately
- **Use collections** to organize by topic

### Bookmark Organization
- **Create topic-based collections**: "Python Tutorials", "Music", etc.
- **Add descriptive notes**: Help remember why you bookmarked
- **Regular cleanup**: Remove outdated bookmarks
- **Use meaningful collection names**

### Performance Optimization
- **Close application** when not in use to free memory
- **Periodic cache cleanup**: Delete cache directory occasionally
- **Limit search results**: Use smaller max_results for faster searches
- **Organize bookmarks**: Keep collections manageable

### Data Management
- **Regular backups**: Copy `bookmarks.json` file
- **Export important bookmarks**: Copy data before major changes
- **Monitor disk space**: Ensure adequate storage for cache

## 🎓 Learning Path

### Beginner (First 15 minutes)
1. **Basic search**: Try searching for videos
2. **Bookmark content**: Add a few bookmarks
3. **Explore interface**: Familiarize with panels
4. **Try keyboard shortcuts**: Use B and Delete keys

### Intermediate (Next 30 minutes)
1. **Create collections**: Organize bookmarks by topic
2. **Add notes**: Enhance bookmarks with personal notes
3. **Playlist exploration**: Search and explore playlists
4. **Batch operations**: Select and manage multiple items

### Advanced (Ongoing)
1. **Optimize workflow**: Develop personal organization system
2. **Master shortcuts**: Use all keyboard shortcuts efficiently
3. **Customize setup**: Adjust settings for your needs
4. **Data management**: Implement backup and cleanup routines

---

## 🏁 Conclusion

YouTube Explorer provides a powerful yet intuitive way to discover and organize YouTube content. With its comprehensive search capabilities, flexible bookmark system, and user-friendly interface, you can efficiently manage your YouTube exploration workflow.

**Key Benefits:**
- ✅ **Fast content discovery** with powerful search
- ✅ **Organized bookmark management** with collections
- ✅ **Persistent storage** of your curated content
- ✅ **Intuitive interface** with keyboard shortcuts
- ✅ **Reliable performance** with caching and error handling

Start exploring and building your personalized YouTube content library today!

---

*User Guide V0 - June 17, 2025*
