import unittest
import os
import requests
from unittest.mock import patch, MagicMock
from logic.youtube_api import YouTubeAPI
from data.cache_manager import CacheManager

class TestYouTubeAPI(unittest.TestCase):
    """
    Feature Name: YouTube API Unit Tests
        Unit tests for the YouTubeAPI class, focusing on individual method functionality
        and error handling in isolation.

    Requirements:
        - Test API key validation.
        - Test successful video search.
        - Test successful playlist search.
        - Test successful playlist video retrieval.
        - Test successful video details retrieval.
        - Test API error handling (quota, network, invalid responses).
        - Test cache integration.

    Parameters:
        None

    Returns:
        None

    Raises:
        AssertionError: If any test assertion fails.

    Example:
        Run tests using: python -m unittest tests/unit/test_youtube_api.py
    """

    @patch.dict(os.environ, {"API_KEY": "test_api_key"})
    @patch('data.cache_manager.CacheManager')
    def setUp(self, MockCacheManager):
        """Set up for test methods."""
        self.mock_cache_manager_instance = MockCacheManager.return_value
        self.youtube_api = YouTubeAPI()
        self.youtube_api.cache_manager = self.mock_cache_manager_instance # Ensure the mock is used

    @patch.dict(os.environ, {}, clear=True)
    def test_init_no_api_key(self):
        """Test initialization when API_KEY is not set."""
        with self.assertRaisesRegex(ValueError, "API_KEY environment variable not set."):
            YouTubeAPI()

    @patch('requests.get')
    def test_search_videos_success(self, mock_get):
        """Test successful video search."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"items": [{"id": {"videoId": "video1"}, "snippet": {"title": "Test Video"}}]}
        mock_get.return_value = mock_response

        self.mock_cache_manager_instance.get_cached_response.return_value = None

        results = self.youtube_api.search_videos("test query")
        self.assertIsNotNone(results)
        self.assertIn("items", results)
        self.assertEqual(len(results["items"]), 1)
        self.mock_cache_manager_instance.cache_response.assert_called_once()

    @patch('requests.get')
    def test_search_videos_from_cache(self, mock_get):
        """Test video search retrieves from cache."""
        cached_data = {"items": [{"id": {"videoId": "cached_video"}, "snippet": {"title": "Cached Video"}}]}
        self.mock_cache_manager_instance.get_cached_response.return_value = cached_data

        results = self.youtube_api.search_videos("test query")
        self.assertEqual(results, cached_data)
        mock_get.assert_not_called()
        self.mock_cache_manager_instance.cache_response.assert_not_called()

    @patch('requests.get')
    def test_search_videos_api_error(self, mock_get):
        """Test video search handles API errors."""
        mock_response = MagicMock()
        mock_response.status_code = 403
        mock_response.text = "Quota Exceeded"
        mock_get.return_value = mock_response

        self.mock_cache_manager_instance.get_cached_response.return_value = None

        with self.assertRaisesRegex(requests.exceptions.RequestException, "YouTube API quota exceeded or access denied."):
            self.youtube_api.search_videos("test query")

    @patch('requests.get')
    def test_search_playlists_success(self, mock_get):
        """Test successful playlist search."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"items": [{"id": {"playlistId": "playlist1"}, "snippet": {"title": "Test Playlist"}}]}
        mock_get.return_value = mock_response

        self.mock_cache_manager_instance.get_cached_response.return_value = None

        results = self.youtube_api.search_playlists("test query")
        self.assertIsNotNone(results)
        self.assertIn("items", results)
        self.assertEqual(len(results["items"]), 1)
        self.mock_cache_manager_instance.cache_response.assert_called_once()

    @patch('requests.get')
    def test_get_playlist_videos_success(self, mock_get):
        """Test successful retrieval of playlist videos."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"items": [{"snippet": {"playlistId": "playlist1", "title": "Video in Playlist"}}]}
        mock_get.return_value = mock_response

        self.mock_cache_manager_instance.get_cached_response.return_value = None

        results = self.youtube_api.get_playlist_videos("playlist123")
        self.assertIsNotNone(results)
        self.assertIn("items", results)
        self.assertEqual(len(results["items"]), 1)
        self.mock_cache_manager_instance.cache_response.assert_called_once()

    @patch('requests.get')
    def test_get_video_details_success(self, mock_get):
        """Test successful retrieval of video details."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"items": [{"id": "video1", "snippet": {"title": "Detailed Video"}, "statistics": {"viewCount": "100"}}]}
        mock_get.return_value = mock_response

        self.mock_cache_manager_instance.get_cached_response.return_value = None

        results = self.youtube_api.get_video_details("video123")
        self.assertIsNotNone(results)
        self.assertIn("items", results)
        self.assertEqual(len(results["items"]), 1)
        self.mock_cache_manager_instance.cache_response.assert_called_once()

    @patch('requests.get')
    def test_handle_api_error_400(self, mock_get):
        """Test _handle_api_error for 400 status code."""
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_get.return_value = mock_response

        self.mock_cache_manager_instance.get_cached_response.return_value = None

        with self.assertRaisesRegex(requests.exceptions.RequestException, "YouTube API bad request."):
            self.youtube_api.search_videos("test query")

    @patch('requests.get')
    def test_handle_api_error_404(self, mock_get):
        """Test _handle_api_error for 404 status code."""
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.text = "Not Found"
        mock_get.return_value = mock_response

        self.mock_cache_manager_instance.get_cached_response.return_value = None

        with self.assertRaisesRegex(requests.exceptions.RequestException, "YouTube API resource not found."):
            self.youtube_api.search_videos("test query")

    @patch('requests.get')
    def test_handle_api_error_other(self, mock_get):
        """Test _handle_api_error for other status codes."""
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError(response=mock_response)
        mock_get.return_value = mock_response

        self.mock_cache_manager_instance.get_cached_response.return_value = None

        with self.assertRaises(requests.exceptions.HTTPError):
            self.youtube_api.search_videos("test query")

if __name__ == '__main__':
    unittest.main()