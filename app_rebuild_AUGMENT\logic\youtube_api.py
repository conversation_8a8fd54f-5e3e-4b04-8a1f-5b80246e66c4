from dotenv import load_dotenv
import os
import requests
from data.cache_manager import CacheManager

# Load environment variables
load_dotenv()

class YouTubeAPI:
    """
    Feature Name: YouTube Data API Wrapper
        Provides methods to search and retrieve information about YouTube videos and playlists.

    Requirements:
        - API: YouTube Data API v3
        - Dependencies: python-dotenv, requests, data.cache_manager
        - Constraints: API key must be set in environment variables.

    Parameters:
        None

    Returns:
        None

    Raises:
        requests.exceptions.RequestException: For network-related errors or bad HTTP responses.
        ValueError: If API_KEY is not found in environment variables.

    Example:
        api = YouTubeAPI()
        videos = api.search_videos("Python tutorials")
        playlists = api.search_playlists("Machine Learning playlists")
    """
    
    BASE_URL = "https://www.googleapis.com/youtube/v3"

    def __init__(self):
        """Initialize YouTube API wrapper.
        
        Sets up API key from environment variables and initializes cache manager.
        """
        self.api_key = os.getenv("API_KEY")
        if not self.api_key:
            raise ValueError("API_KEY environment variable not set.")
        self.cache_manager = CacheManager()
        self.validate_requirements()
        self.setup_error_handling()
        self.initialize_logging()

    def search_videos(self, query, max_results=10):
        """Search for YouTube videos.
        
        Args:
            query: Search query string
            max_results: Maximum number of results to return (default: 10)
            
        Returns:
            dict: Search results containing video information
        """
        params = {
            "part": "snippet",
            "q": query,
            "type": "video",
            "maxResults": max_results,
            "key": self.api_key
        }
        
        # Try to get from cache first
        cached_response = self.cache_manager.get_cached_response("search_videos", params)
        if cached_response:
            return cached_response
            
        # If not in cache, make API call
        url = f"{self.BASE_URL}/search"
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            self.cache_manager.cache_response("search_videos", params, data)
            return data
        else:
            # Error handling for API calls
            self._handle_api_error(response)

    def search_playlists(self, query, max_results=10):
        """Search for YouTube playlists.
        
        Args:
            query: Search query string
            max_results: Maximum number of results to return (default: 10)
            
        Returns:
            dict: Search results containing playlist information
        """
        params = {
            "part": "snippet",
            "q": query,
            "type": "playlist",
            "maxResults": max_results,
            "key": self.api_key
        }
        
        # Try to get from cache first
        cached_response = self.cache_manager.get_cached_response("search_playlists", params)
        if cached_response:
            return cached_response

        # If not in cache, make API call
        url = f"{self.BASE_URL}/search"
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            self.cache_manager.cache_response("search_playlists", params, data)
            return data
        else:
            # Error handling for API calls
            self._handle_api_error(response)

    def get_playlist_videos(self, playlist_id, max_results=10):
        """Get videos from a YouTube playlist.
        
        Args:
            playlist_id: ID of the playlist
            max_results: Maximum number of results to return (default: 10)
            
        Returns:
            dict: Playlist items containing video information
        """
        params = {
            "part": "snippet",
            "playlistId": playlist_id,
            "maxResults": max_results,
            "key": self.api_key
        }
        
        # Try to get from cache first
        cached_response = self.cache_manager.get_cached_response("get_playlist_videos", params)
        if cached_response:
            return cached_response

        # If not in cache, make API call
        url = f"{self.BASE_URL}/playlistItems"
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            self.cache_manager.cache_response("get_playlist_videos", params, data)
            return data
        else:
            # Error handling for API calls
            self._handle_api_error(response)
            
    def get_video_details(self, video_id):
        """Get details of a YouTube video.
        
        Args:
            video_id: ID of the video
            
        Returns:
            dict: Video details including snippet and statistics
        """
        params = {
            "part": "snippet,statistics",
            "id": video_id,
            "key": self.api_key
        }
        
        # Try to get from cache first
        cached_response = self.cache_manager.get_cached_response("get_video_details", params)
        if cached_response:
            return cached_response

        # If not in cache, make API call
        url = f"{self.BASE_URL}/videos"
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            self.cache_manager.cache_response("get_video_details", params, data)
            return data
        else:
            # Error handling for API calls
            self._handle_api_error(response)

    def validate_requirements(self):
        """
        Validates that essential requirements for the API are met.
        Ensures the API key is present.
        """
        if not self.api_key:
            raise ValueError("API_KEY is not set. Please set the API_KEY environment variable.")
        # Additional validation for API quota limits could be added here if a way to check it programmatically exists.
        print("YouTubeAPI requirements validated.")

    def setup_error_handling(self):
        """
        Sets up error handling mechanisms for API calls.
        This method primarily outlines the strategy; actual error handling is within API methods.
        """
        # This method serves as a placeholder for more complex error handling setup
        # such as registering custom error handlers or configuring retry strategies globally.
        print("YouTubeAPI error handling setup.")

    def initialize_logging(self):
        """
        Initializes logging for the YouTubeAPI class.
        """
        # Assuming a logger is already configured globally or passed in.
        # For now, using print statements as a placeholder for actual logging.
        print("YouTubeAPI logging initialized.")

    def _handle_api_error(self, response):
        """
        Handles API errors based on the HTTP response status code.
        """
        if response.status_code == 403:
            print(f"API Error: Quota Exceeded or Access Denied. Response: {response.text}")
            # Implement user-friendly message + retry after logic
            raise requests.exceptions.RequestException("YouTube API quota exceeded or access denied.")
        elif response.status_code == 400:
            print(f"API Error: Bad Request. Response: {response.text}")
            raise requests.exceptions.RequestException("YouTube API bad request.")
        elif response.status_code == 404:
            print(f"API Error: Not Found. Response: {response.text}")
            raise requests.exceptions.RequestException("YouTube API resource not found.")
        else:
            print(f"API Error: {response.status_code} - {response.text}")
            raise requests.exceptions.RequestException(f"YouTube API error: {response.status_code}")
