import json
import os
from datetime import datetime

class BookmarkManager:
    """Manages YouTube video and playlist bookmarks.
    
    This class handles the storage, retrieval, and organization of
    bookmarked YouTube videos and playlists, including collections
    for grouping bookmarks.
    
    Attributes:
        storage_file: Path to JSON file for bookmark storage
        bookmarks: Dictionary of bookmarked items
        collections: Dictionary of bookmark collections
    """
    
    def __init__(self, storage_file="bookmarks.json"):
        """Initialize bookmark manager.
        
        Args:
            storage_file: Path to JSON storage file (default: bookmarks.json)
        """
        self.storage_file = storage_file
        self.bookmarks = {}
        self.collections = {"Default": []}
        self.load_data()

    def load_data(self):
        """Load bookmarks and collections from storage file."""
        # Start with default empty state
        self.bookmarks = {}
        self.collections = {"Default": []}
        
        if os.path.exists(self.storage_file):
            try:
                with open(self.storage_file, "r") as file:
                    data = json.load(file)
                    loaded_bookmarks = data.get("bookmarks", {})
                    loaded_collections = data.get("collections", {"Default": []})
                    
                    # First ensure all collections exist
                    for collection_name, items in loaded_collections.items():
                        if collection_name not in self.collections:
                            self.collections[collection_name] = []
                    
                    # Now load bookmarks and update their collections
                    for bookmark_id, bookmark in loaded_bookmarks.items():
                        # Ensure bookmark has a collection
                        collection = bookmark.get("Collection", "Default")
                        if collection not in self.collections:
                            self.collections[collection] = []
                        
                        # Save bookmark
                        self.bookmarks[bookmark_id] = bookmark
                        
                        # Add to collection if not already there
                        if bookmark_id not in self.collections[collection]:
                            self.collections[collection].append(bookmark_id)
                    
                    # Save to ensure data consistency
                    self.save_data()
                    
            except Exception as e:
                print(f"Error loading data: {e}")
                # Keep initial empty state
                self.bookmarks = {}
                self.collections = {"Default": []}
        self.save_data()  # Ensure data structure is consistent

    def save_data(self):
        """Save bookmarks and collections to storage file."""
        try:
            data = {
                "bookmarks": self.bookmarks,
                "collections": self.collections
            }
            with open(self.storage_file, "w") as file:
                json.dump(data, file, indent=4)
        except Exception as e:
            print(f"Error saving data: {e}")

    def add_bookmark(self, item_id, details, collection_name="Default"):
        """Add a new bookmark to the specified collection.
        
        Args:
            item_id: Unique identifier for the bookmark
            details: Dictionary of bookmark details
            collection_name: Name of collection to add to (default: "Default")
        """
        try:
            # Make a copy to avoid modifying the original
            details = details.copy()
            
            # Use specified collection or details' collection or Default
            coll_name = collection_name or details.get("Collection", "Default")
            details["Collection"] = coll_name
            
            # Add bookmark
            self.bookmarks[item_id] = details
            
            # Ensure collection exists
            if coll_name not in self.collections:
                self.collections[coll_name] = []
                
            # Remove from any existing collections
            for collection in self.collections.values():
                if item_id in collection:
                    collection.remove(item_id)
                
            # Add to new collection
            if item_id not in self.collections[coll_name]:
                self.collections[coll_name].append(item_id)
                
            self.save_data()
            
        except Exception as e:
            print(f"Error adding bookmark: {e}")

    def remove_bookmark(self, item_id):
        """Remove a bookmark.
        
        Args:
            item_id: Unique identifier for the bookmark to remove
        """
        if item_id in self.bookmarks:
            # Get collection before removing bookmark
            collection = self.bookmarks[item_id].get("Collection", "Default")
            
            # Remove bookmark
            del self.bookmarks[item_id]
            
            # Remove from collection
            if collection in self.collections and item_id in self.collections[collection]:
                self.collections[collection].remove(item_id)
                
            self.save_data()

    def get_collections(self):
        """Get list of all collection names.
        
        Returns:
            list: Sorted list of collection names
        """
        return sorted(list(self.collections.keys()))

    def add_collection(self, name):
        """Add a new collection.
        
        Args:
            name: Name of the new collection
        """
        if name not in self.collections:
            self.collections[name] = []
            self.save_data()

    def remove_collection(self, collection_name):
        """Remove a collection and move its bookmarks to Default.
        
        Args:
            collection_name: Name of collection to remove
            
        Returns:
            bool: True if collection was removed, False otherwise
        """
        if collection_name == "Default":
            return False  # Cannot remove Default collection
            
        if collection_name in self.collections:
            # Move all bookmarks to Default collection
            for bookmark_id in self.collections[collection_name]:
                if bookmark_id in self.bookmarks:
                    self.bookmarks[bookmark_id]["Collection"] = "Default"
                    if bookmark_id not in self.collections["Default"]:
                        self.collections["Default"].append(bookmark_id)
            
            # Remove the collection
            del self.collections[collection_name]
            self.save_data()
            return True
        return False

    def move_to_collection(self, bookmark_id, collection):
        """Move a bookmark to a different collection.
        
        Args:
            bookmark_id: ID of bookmark to move
            collection: Target collection name
        """
        if bookmark_id in self.bookmarks:
            # Ensure target collection exists
            if collection not in self.collections:
                self.collections[collection] = []
            
            # Update bookmark's collection
            old_collection = self.bookmarks[bookmark_id].get("Collection", "Default")
            self.bookmarks[bookmark_id]["Collection"] = collection
            
            # Remove from old collection
            if old_collection in self.collections and bookmark_id in self.collections[old_collection]:
                self.collections[old_collection].remove(bookmark_id)
            
            # Add to new collection
            if bookmark_id not in self.collections[collection]:
                self.collections[collection].append(bookmark_id)
                
            self.save_data()

    def update_bookmark_notes(self, item_id, notes):
        """Update notes for a bookmark.
        
        Args:
            item_id: Bookmark ID
            notes: New notes text
            
        Returns:
            bool: True if updated successfully
        """
        if item_id in self.bookmarks:
            self.bookmarks[item_id]["Notes"] = notes
            self.save_data()
            return True
        return False

    def get_all_bookmarks(self):
        """Get all bookmarks.
        
        Returns:
            dict: All bookmarks
        """
        return self.bookmarks

    def get_collection_bookmarks(self, collection_name):
        """Get bookmarks from a specific collection.
        
        Args:
            collection_name: Name of the collection
            
        Returns:
            dict: Bookmarks in the specified collection
        """
        if collection_name not in self.collections:
            return {}
            
        collection_bookmarks = {}
        for bookmark_id in self.collections[collection_name]:
            if bookmark_id in self.bookmarks:
                collection_bookmarks[bookmark_id] = self.bookmarks[bookmark_id]
        return collection_bookmarks
