# Troubleshooting Guide - YouTube Explorer Application

## 🚨 Common Issues and Solutions

### 1. Application Won't Start

#### Issue: "API_KEY environment variable not set"
**Symptoms:**
- Application crashes on startup
- Error message about missing API_KEY

**Solutions:**
1. **Check .env file exists in root directory**
   ```bash
   # Verify file exists
   ls -la .env
   ```

2. **Verify .env file content**
   ```bash
   # Content should be:
   API_KEY=your_actual_youtube_api_key_here
   ```

3. **Get a valid YouTube API key**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing
   - Enable YouTube Data API v3
   - Create credentials (API Key)
   - Copy key to .env file

#### Issue: "ModuleNotFoundError"
**Symptoms:**
- Import errors on startup
- Missing Python packages

**Solutions:**
1. **Install required dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Check Python version**
   ```bash
   python --version  # Should be 3.x
   ```

3. **Use virtual environment (recommended)**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

#### Issue: "Permission denied" or file access errors
**Symptoms:**
- Cannot create bookmarks.json
- Cannot write to log files

**Solutions:**
1. **Check directory permissions**
   ```bash
   # Ensure write permissions in project directory
   chmod 755 .
   ```

2. **Run as administrator (Windows)**
   - Right-click command prompt
   - Select "Run as administrator"

3. **Check disk space**
   ```bash
   df -h  # Linux/Mac
   dir   # Windows
   ```

---

### 2. Search Functionality Issues

#### Issue: "Search returns no results"
**Symptoms:**
- Search completes but shows empty results
- No error messages displayed

**Solutions:**
1. **Verify API key is valid**
   ```python
   # Test API key manually
   python test_api_key.py
   ```

2. **Check internet connection**
   ```bash
   ping google.com
   ```

3. **Try different search terms**
   - Use common keywords
   - Avoid special characters
   - Try single words first

4. **Check API quota**
   - Visit Google Cloud Console
   - Check API usage dashboard
   - Verify quota not exceeded

#### Issue: "Search is very slow"
**Symptoms:**
- Search takes > 5 seconds
- UI becomes unresponsive

**Solutions:**
1. **Check network speed**
   ```bash
   speedtest-cli  # If installed
   ```

2. **Reduce max_results parameter**
   ```python
   # In search calls, use smaller numbers
   api.search_videos(query, max_results=5)  # Instead of 10+
   ```

3. **Clear cache**
   ```bash
   # Delete cache directory
   rm -rf cache/
   ```

4. **Check system resources**
   ```bash
   # Monitor CPU and memory usage
   top  # Linux/Mac
   taskmgr  # Windows
   ```

---

### 3. Bookmark System Issues

#### Issue: "Bookmarks not saving"
**Symptoms:**
- Bookmarks disappear after restart
- No bookmarks.json file created

**Solutions:**
1. **Check file permissions**
   ```bash
   # Ensure write permissions
   touch bookmarks.json
   chmod 644 bookmarks.json
   ```

2. **Verify JSON format**
   ```python
   # Check if bookmarks.json is valid JSON
   import json
   with open('bookmarks.json', 'r') as f:
       json.load(f)
   ```

3. **Check disk space**
   - Ensure sufficient storage available
   - Clean up temporary files

#### Issue: "Bookmark icons not displaying"
**Symptoms:**
- Stars (★/☆) show as squares or question marks
- UI symbols appear broken

**Solutions:**
1. **Check font support**
   - Install Unicode-compatible fonts
   - Update system fonts

2. **Use fallback symbols**
   ```python
   # Modify symbol definitions in app.py
   'bookmark': {'symbol': '[*]', 'tooltip': 'Bookmarked'},
   'unbookmark': {'symbol': '[ ]', 'tooltip': 'Not bookmarked'}
   ```

3. **Update display settings**
   - Increase font size
   - Change system encoding to UTF-8

---

### 4. UI and Display Issues

#### Issue: "Window too small or elements cut off"
**Symptoms:**
- UI elements not visible
- Scrollbars missing
- Text truncated

**Solutions:**
1. **Adjust window size**
   ```python
   # In app.py, modify geometry
   self.root.geometry("1400x800")  # Increase from 1200x700
   ```

2. **Check screen resolution**
   - Ensure minimum 1024x768 resolution
   - Adjust scaling settings

3. **Enable scrollbars**
   ```python
   # Add scrollbars to treeviews
   scrollbar = ttk.Scrollbar(parent)
   tree.config(yscrollcommand=scrollbar.set)
   ```

#### Issue: "Application freezes during search"
**Symptoms:**
- UI becomes unresponsive
- Cannot click buttons
- Must force-quit application

**Solutions:**
1. **Implement threading**
   ```python
   import threading
   
   def search_thread():
       # Move API calls to separate thread
       pass
   
   threading.Thread(target=search_thread).start()
   ```

2. **Add progress indicators**
   ```python
   # Show loading spinner during operations
   progress = ttk.Progressbar(parent, mode='indeterminate')
   progress.start()
   ```

3. **Reduce API timeout**
   ```python
   # In youtube_api.py
   response = requests.get(url, params=params, timeout=10)
   ```

---

### 5. Performance Issues

#### Issue: "High memory usage"
**Symptoms:**
- Application uses > 500MB RAM
- System becomes slow
- Out of memory errors

**Solutions:**
1. **Clear cache regularly**
   ```python
   # Implement cache cleanup
   self.cache_manager.clear_old_entries()
   ```

2. **Limit search results**
   ```python
   # Reduce max_results in API calls
   max_results = min(requested_results, 10)
   ```

3. **Optimize data structures**
   ```python
   # Use generators instead of lists for large datasets
   def get_videos():
       for video in video_list:
           yield video
   ```

#### Issue: "Slow startup time"
**Symptoms:**
- Application takes > 10 seconds to start
- Long delay before UI appears

**Solutions:**
1. **Lazy load components**
   ```python
   # Initialize heavy components only when needed
   self.api = None  # Initialize later
   ```

2. **Optimize imports**
   ```python
   # Move heavy imports inside functions
   def search_videos(self):
       import pandas as pd  # Only when needed
   ```

3. **Precompile Python files**
   ```bash
   python -m compileall .
   ```

---

### 6. Network and API Issues

#### Issue: "API quota exceeded"
**Symptoms:**
- Error 403 from YouTube API
- "Quota exceeded" messages

**Solutions:**
1. **Monitor API usage**
   - Check Google Cloud Console
   - Track daily quota consumption

2. **Implement caching**
   ```python
   # Cache API responses to reduce calls
   @lru_cache(maxsize=100)
   def cached_search(query):
       return api.search_videos(query)
   ```

3. **Request quota increase**
   - Contact Google Cloud Support
   - Justify increased quota needs

#### Issue: "Network timeout errors"
**Symptoms:**
- Requests fail with timeout
- Intermittent connectivity issues

**Solutions:**
1. **Increase timeout values**
   ```python
   requests.get(url, timeout=30)  # Increase from default
   ```

2. **Implement retry logic**
   ```python
   import time
   
   for attempt in range(3):
       try:
           response = requests.get(url)
           break
       except requests.Timeout:
           time.sleep(2 ** attempt)  # Exponential backoff
   ```

3. **Check firewall settings**
   - Allow Python through firewall
   - Check corporate proxy settings

---

## 🔧 Debug Mode

### Enable Detailed Logging
```python
# In main.py or app.py
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Check Log Files
```bash
# View recent log entries
tail -f youtube_explorer.log

# Search for specific errors
grep "ERROR" youtube_explorer.log
```

### Performance Profiling
```python
import cProfile
import pstats

# Profile the application
cProfile.run('app.run()', 'profile_stats')
stats = pstats.Stats('profile_stats')
stats.sort_stats('cumulative').print_stats(10)
```

---

## 📞 Getting Help

### Before Reporting Issues
1. **Check this troubleshooting guide**
2. **Review error logs**
3. **Test with minimal example**
4. **Verify system requirements**

### Information to Include in Bug Reports
- Operating system and version
- Python version
- Complete error message
- Steps to reproduce
- Expected vs actual behavior
- Log file excerpts

### Useful Commands for Diagnostics
```bash
# System information
python --version
pip list
python -c "import tkinter; print('Tkinter available')"

# Test API connectivity
python test_api_key.py

# Check file permissions
ls -la bookmarks.json
ls -la youtube_explorer.log

# Monitor resource usage
python -c "import psutil; print(f'Memory: {psutil.virtual_memory().percent}%')"
```

---

*Troubleshooting guide last updated: June 2025*
