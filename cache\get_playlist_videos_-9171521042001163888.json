{"timestamp": "2025-06-04T18:33:52.004233", "response": {"kind": "youtube#playlistItemListResponse", "etag": "O_SGs_VaIBiXtJ15CtsHHWT7XPY", "nextPageToken": "EAAaHlBUOkNBb2lFRVF3UVRCRlJqa3pSRU5GTlRjME1rSQ", "items": [{"kind": "youtube#playlistItem", "etag": "3RX8fV3KBG53R3zK-p4bbrRsqV0", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy41NkI0NEY2RDEwNTU3Q0M2", "snippet": {"publishedAt": "2021-07-23T07:08:24Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started - Introduction ( Learning Path 01 )", "description": "This is the first tutorial of a new series to get you started in E3D. We are going through the basics for new users who does plant design with other software. For PDMS users, this might help you move to E3D. And you can also pick a tip or two looking how we use the software. We are trying to get you started in the shortest possible time, so tell us if we are doing too brief or we are too detail. We want to be able to put a new tutorial every fortnight while we continue to share tips and how to in the other series. \nYour interaction keeps us going. Please subscribe , comments and \"like or dislike \" us.\n\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/5c4LTTz-gGQ/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/5c4LTTz-gGQ/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/5c4LTTz-gGQ/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/5c4LTTz-gGQ/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/5c4LTTz-gGQ/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 0, "resourceId": {"kind": "youtube#video", "videoId": "5c4LTTz-gGQ"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "jPfZAf0xIoU6dEdDtEFPektAtfM", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy4yODlGNEE0NkRGMEEzMEQy", "snippet": {"publishedAt": "2021-07-30T08:23:08Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started - Equipment Modelling  ( Learning Path 02 )", "description": "In Tutorial 2 of getting started with E3D, we introduce concepts of equipment modelling and show you how to use primitive and catalogue component to model a tank with nozzle. This should give you a start and you can experiment with the various features in E3D to model equipment. We are encouraged by all your support,. Thank you. \n\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/cL9JAaMV30I/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/cL9JAaMV30I/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/cL9JAaMV30I/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/cL9JAaMV30I/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/cL9JAaMV30I/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 1, "resourceId": {"kind": "youtube#video", "videoId": "cL9JAaMV30I"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "tgyNzbmk8q5w9neF5hGWrVbu5eI", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy4wMTcyMDhGQUE4NTIzM0Y5", "snippet": {"publishedAt": "2022-01-11T01:38:16Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started - Equipment Modification ( Learning Path 03 )", "description": "In Tutorial 3 of getting started with E3D, we look at a few modification tools to modify the equipment that we created. Some of these tools are available in PDMS and E3D while the graphical manipulation tool is only in E3D. These graphical E3D tool is more intiutive and should help new users overcome the usage of E3D. \n\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/qDUnNG6Gsvg/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/qDUnNG6Gsvg/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/qDUnNG6Gsvg/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/qDUnNG6Gsvg/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/qDUnNG6Gsvg/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 2, "resourceId": {"kind": "youtube#video", "videoId": "qDUnNG6Gsvg"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "dxGwJWTGTGxlsRb9TL4hOTwK86w", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy41MjE1MkI0OTQ2QzJGNzNG", "snippet": {"publishedAt": "2022-01-17T01:57:28Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started : In-canvas Editor ( Learning Path 04 )", "description": "In this tutorial we look at the In-canvas Editor. This is a feature of E3D not found in PDMS. It is used in primitive creation and various manipulation in the 3D view. We have created a new playlist \"E3D for PDMS users\" and added E3D feature into this playlist to help users of PDMS.\n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/GHC04mLekhE/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 3, "resourceId": {"kind": "youtube#video", "videoId": "GHC04mLekhE"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "lFf8Y2y-kZ_upZ_xxoqz55cPkP0", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy4wOTA3OTZBNzVEMTUzOTMy", "snippet": {"publishedAt": "2022-01-21T03:19:20Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started  - Using Event Driven Graphics EDG ( ( Learning Path 00 )", "description": "Event Driven Graphics or EDG was how we interact with the 3D canvas before the In-canvas Editor. It is a powerful tool and using it will make the modeling work more efficient. Even in E3D 3.1 , EDG is still being used. So your investment in learning EDG will not be wasted as you will be making use of it for years before it is being totally replaced. \n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/7BqHEEoGqkU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 4, "resourceId": {"kind": "youtube#video", "videoId": "7BqHEEoGqkU"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "NIajHtP7vIj7XQ7c4t0VBUbCanE", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy4xMkVGQjNCMUM1N0RFNEUx", "snippet": {"publishedAt": "2022-01-28T07:58:08Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started - Local coordinate system and In-canvas commands (  learning Path 00 )", "description": "We look at more E3D feature. The local coordinate system and how to use them as well as the highly convenient in-canvas command. \n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/toRQH-5P8VQ/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 5, "resourceId": {"kind": "youtube#video", "videoId": "toRQH-5P8VQ"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "3S3-8md74pSpHENTkgidKnJFI14", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy41MzJCQjBCNDIyRkJDN0VD", "snippet": {"publishedAt": "2022-02-24T03:29:27Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started Structural - Grid ( 02-01)", "description": "Let's get started with Structural Modelling in E3D. We wll get you started by showing the steps to create a structural frame starting with the grid up to the final structure. Hope this simple lesson will get you to begin modelling  structure in E3D. \n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/I42rv45HiGs/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 6, "resourceId": {"kind": "youtube#video", "videoId": "I42rv45HiGs"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "ia-CS5aWpK_SKPH4FSU3qUj8nPE", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy5DQUNERDQ2NkIzRUQxNTY1", "snippet": {"publishedAt": "2022-03-01T03:38:30Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started Structural - Portal Frame ( 02-02)", "description": "Continuing from where we stop, we will create a portal frame. In this video you can watch for the technique we use to create the frame. In this way, it will help you to choose the best method to model the structure.  After this we will do the last video to complate the model.\n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/VDUmev1eTMU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 7, "resourceId": {"kind": "youtube#video", "videoId": "VDUmev1eTMU"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "mzdTWR802kRXGUyhrkwIeDIi-ZU", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy5GNjNDRDREMDQxOThCMDQ2", "snippet": {"publishedAt": "2022-03-09T07:06:22Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started Structural - Completing the Structure ( 02-03)", "description": "We will introduce a few feature in E3D structure to complete our structure.\n\nWe will be using the :\na. Offset/Distance - to set a distance from the pline that we create. \nb. Local co-ordinate - to model purlin along the roof.\n\nHope this 3 videos get you started and explore the possibility with E3D Structure modelling. \n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/gJzoA-o3hL0/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 8, "resourceId": {"kind": "youtube#video", "videoId": "gJzoA-o3hL0"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}, {"kind": "youtube#playlistItem", "etag": "loYGGKH9FNvyiIo5vdGyXgi65sw", "id": "UExqQ2hrb2dfcF9MRUV3cXh2ZG5pV1RLN0psRW5EWFVXXy40NzZCMERDMjVEN0RFRThB", "snippet": {"publishedAt": "2022-03-17T06:58:10Z", "channelId": "UCvlBphRK33T6VYhTlpqhHPw", "title": "E3D Getting Started Structural -  ASL Layout  ( 02-04 )", "description": "We will show you how to create a platform using plate with stairs and railings. E3D has 2 mode of creating Access Stairs and Ladders, the layout mode is a simplified form of ASL where the details of the components are not considered. The detailed mode includes selecting the beams and columns as well as fitting for the ASL.\n\nWe will be using the :\na. Offset in EDG . \nb. Local co-ordinate to snap the beam to the correct height.\nc. Demonstrate how to use Stairs and Railings in ASL for E3D.\n\nIf you have any comments, feel free to comment. We can only improve with your comments.  Thanks for watching.\n\nIf you need to use AVEVA E3D to advance your business and deliver your goals, TDS Engr Solutions Pte Ltd provide consultation to get you on your way. \nIf you need customisation and creation of catalog and specifications for your project, we are available to do this. \nWe also provide automation for E3D deliverables as well as interfaces to other software. \nContact us : <EMAIL>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/ZGiwfs2NdCc/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/ZGiwfs2NdCc/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/ZGiwfs2NdCc/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/ZGiwfs2NdCc/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/ZGiwfs2NdCc/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "TDS Engr Solutions Pte Ltd", "playlistId": "PLjChkog_p_LEEwqxvdniWTK7JlEnDXUW_", "position": 9, "resourceId": {"kind": "youtube#video", "videoId": "ZGiwfs2NdCc"}, "videoOwnerChannelTitle": "TDS Engr Solutions Pte Ltd", "videoOwnerChannelId": "UCvlBphRK33T6VYhTlpqhHPw"}}], "pageInfo": {"totalResults": 11, "resultsPerPage": 10}}}