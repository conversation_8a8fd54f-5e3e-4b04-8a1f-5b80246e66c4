"""Main GUI application for YouTube Explorer"""
from utils.logger import <PERSON><PERSON><PERSON><PERSON><PERSON>
import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
import webbrowser
import time
import os
from datetime import datetime
from logic.youtube_api import YouTubeAPI
from data.bookmark_manager import BookmarkManager
from utils.ui_helpers import <PERSON><PERSON>bolManager, TooltipManager, ErrorManager, AnimationManager

class YouTubeExplorerApp:
    """YouTube Explorer Application

    Purpose:
        Main application class for YouTube content exploration and bookmarking.
        Provides search, viewing, and bookmark management for videos and playlists.

    Requirements:
        - Python 3.x
        - tkinter
        - pandas
        - YouTube API access
        - Internet connection
        - Local storage permissions

    Technical Constraints:
        - Memory usage: Below 500MB
        - Response time: Under 300ms for UI actions
        - API rate limits compliance
        
    Dependencies:
        - AppLogger: Application logging
        - YouTubeAPI: YouTube data access
        - BookmarkManager: Bookmark data management
        - UI Helpers: SymbolManager, TooltipManager, ErrorManager

    Example:
        app = YouTubeExplorerApp()
        app.run()
    """
    
    def __init__(self):
        """Initialize the YouTube Explorer application"""
        # Initialize logging first to capture all subsequent events
        self.logger = None  # Initialize to None
        self.error_manager = None # Initialize to None
        try:
            self.initialize_logging()
            self.logger.logger.info("Starting YouTube Explorer application")
            
            # Initialize utility managers that depend on root
            self.root = tk.Tk()
            self.root.title("YouTube Explorer")
            self.root.geometry("1200x700")
            self.tooltip_manager = TooltipManager()
            self.error_manager = ErrorManager(self.root)
            self.animation_manager = AnimationManager(self.root, self.logger)

            # Validate requirements
            self.validate_requirements()

            # Setup error handling
            self.setup_error_handling()
            
            # Initialize symbols for UI
            self.symbols = {
                'bookmark': {'symbol': '★', 'tooltip': 'Click to remove from bookmarks'},
                'unbookmark': {'symbol': '☆', 'tooltip': 'Click to add to bookmarks'},
                'remove': {'symbol': '🗑️', 'tooltip': 'Remove selected items'},
                'add_collection': {'symbol': '📁', 'tooltip': 'Create new collection'},
                'edit': {'symbol': '✏️', 'tooltip': 'Edit notes'},
                'search': {'symbol': '🔍', 'tooltip': 'Search'}
            }
            # Initialize API and data managers
            self.api = YouTubeAPI()
            
            # Initialize bookmark manager with path relative to this file
            bookmarks_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "bookmarks.json")
            self.bookmark_manager = BookmarkManager(bookmarks_path)
            
            self.search_mode = tk.StringVar(value="video")
            
            # Initialize drag and drop data
            self._drag_data = {"item": None, "x": 0, "y": 0}
            
            # Create UI elements
            self.create_widgets()
            self.create_context_menus()
            self.bind_shortcuts()
            
            self.logger.logger.info("Application initialized successfully")
        except Exception as e:
            if self.logger:
                self.logger.logger.error(f"Failed to initialize application: {str(e)}")
            if self.error_manager:
                self.error_manager.show_error("Initialization Error", f"Failed to initialize application: {str(e)}")
            else:
                messagebox.showerror("Error", f"Failed to initialize application: {str(e)}")
            raise

    def validate_requirements(self):
        """
        Validates essential application requirements.
        Ensures necessary modules are available and API key is set.
        """
        try:
            import tkinter
            import pandas
            import requests
            # Check for API key in environment variables
            if not os.getenv("API_KEY"):
                raise ValueError("API_KEY environment variable not set. Please set it to run the application.")
            self.logger.logger.info("Application requirements validated.")
        except ImportError as e:
            self.logger.logger.critical(f"Missing required module: {e}. Please install it.")
            raise SystemExit(f"Missing required module: {e}. Please install it.")
        except ValueError as e:
            self.logger.logger.critical(f"Configuration error: {e}")
            raise SystemExit(f"Configuration error: {e}")
        except Exception as e:
            self.logger.logger.critical(f"An unexpected error occurred during requirement validation: {e}")
            raise SystemExit(f"An unexpected error occurred during requirement validation: {e}")

    def setup_error_handling(self):
        """
        Sets up global error handling for the application.
        This includes configuring the ErrorManager and potentially Tkinter error hooks.
        """
        # Tkinter error handling (uncomment if needed for global Tkinter errors)
        # def tk_exception_handler(exc_type, exc_value, exc_traceback):
        #     self.logger.logger.error(f"Tkinter Error: {exc_value}", exc_info=(exc_type, exc_value, exc_traceback))
        #     self.error_manager.show_error("UI Error", f"An unexpected UI error occurred: {exc_value}")
        # self.root.report_callback_exception = tk_exception_handler
        self.logger.logger.info("Application error handling setup.")

    def initialize_logging(self):
        """
        Initializes the application-wide logger.
        """
        self.logger = AppLogger()
        self.logger.logger.info("Application logging initialized.")

    def create_widgets(self):
        """Create and configure all UI widgets"""
        # Initialize symbols for UI using SymbolManager
        self.symbol_manager = SymbolManager()
        self.symbols = {
            'bookmark': self.symbol_manager.get_symbol('bookmark'),
            'unbookmark': self.symbol_manager.get_symbol('unbookmark'),
            'remove': self.symbol_manager.get_symbol('delete'),
            'add_collection': self.symbol_manager.get_symbol('add_collection', fallback='[+]'), # Using a more generic fallback
            'edit': self.symbol_manager.get_symbol('edit', fallback='[E]'), # Using a more generic fallback
            'search': self.symbol_manager.get_symbol('search', fallback='[S]') # Using a more generic fallback
        }
        try:
            # Search bar and mode selection
            self.search_frame = ttk.Frame(self.root)
            self.search_frame.pack(fill=tk.X, padx=10, pady=10)

            self.search_entry = ttk.Entry(self.search_frame, width=50)
            self.search_entry.pack(side=tk.LEFT, padx=(0, 10))

            self.search_button = ttk.Button(self.search_frame, text="Search", command=self.perform_search)
            self.search_button.pack(side=tk.LEFT)

            # Radio buttons for search mode
            modes = [("Videos", "video"), ("Playlists", "playlist"), ("Both", "both")]
            for text, mode in modes:
                ttk.Radiobutton(self.search_frame, text=text, variable=self.search_mode, value=mode).pack(side=tk.LEFT, padx=5)

            # Double relational panels
            self.panel_frame = ttk.Frame(self.root)
            self.panel_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Video results panel
            self.video_panel = ttk.LabelFrame(self.panel_frame, text="Videos")
            self.video_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
            
            # Video panel toolbar
            self.video_toolbar = ttk.Frame(self.video_panel)
            self.video_toolbar.pack(fill=tk.X, padx=5, pady=2)
            self.video_clear_btn = ttk.Button(
                self.video_toolbar, 
                text="🗑️ Remove Selected",
                command=self.remove_selected_videos
            )
            self.video_clear_btn.pack(side=tk.LEFT)
            
            # Video tree configuration
            self.video_tree = ttk.Treeview(
                self.video_panel, 
                columns=("Title", "Channel", "Views", "Upload Date", "Bookmark"),
                show="headings"
            )
            for col in self.video_tree["columns"]:
                self.video_tree.heading(col, text=col)
            self.video_tree.pack(fill=tk.BOTH, expand=True)
            self.video_tree.bind('<Double-1>', self.open_video_in_browser)
            self.video_tree.bind('<ButtonRelease-1>', self.toggle_video_bookmark)
            self.video_tree.bind('<Delete>', lambda e: self.remove_selected_videos())
            self.video_tree.bind('<Button-3>', self.show_video_context_menu)

            # Playlist results panel
            self.playlist_panel = ttk.LabelFrame(self.panel_frame, text="Playlists")
            self.playlist_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
            
            # Playlist panel toolbar
            self.playlist_toolbar = ttk.Frame(self.playlist_panel)
            self.playlist_toolbar.pack(fill=tk.X, padx=5, pady=2)
            self.playlist_clear_btn = ttk.Button(
                self.playlist_toolbar,
                text="🗑️ Remove Selected",
                command=self.remove_selected_playlists
            )
            self.playlist_clear_btn.pack(side=tk.LEFT)

            self.playlist_tree = ttk.Treeview(self.playlist_panel, columns=("Title", "Channel", "Video Count", "Bookmark"), show="headings")
            for col in self.playlist_tree["columns"]:
                self.playlist_tree.heading(col, text=col)
            self.playlist_tree.pack(fill=tk.BOTH, expand=True)
            self.playlist_tree.bind('<Double-1>', self.open_playlist_in_browser)
            self.playlist_tree.bind('<ButtonRelease-1>', self.toggle_playlist_bookmark)
            self.playlist_tree.bind('<<TreeviewSelect>>', self.on_playlist_select)
            self.playlist_tree.bind('<Delete>', lambda e: self.remove_selected_playlists())
            self.playlist_tree.bind('<Button-3>', self.show_playlist_context_menu)

            # Bookmark management sidebar
            self.bookmark_panel = ttk.LabelFrame(self.root, text="Bookmarks")
            self.bookmark_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=10)
            
            # Collections section at the top
            collections_frame = ttk.Frame(self.bookmark_panel)
            collections_frame.pack(fill=tk.X, padx=5, pady=(5,0))
            
            # Collection label and dropdown in a row
            collection_row = ttk.Frame(collections_frame)
            collection_row.pack(fill=tk.X, pady=(0,5))
            
            ttk.Label(collection_row, text="Collection:").pack(side=tk.LEFT, padx=(0,5))
            
            self.collection_var = tk.StringVar(value="All")
            self.collection_dropdown = ttk.Combobox(
                collection_row,
                textvariable=self.collection_var,
                state="readonly",
                width=15
            )
            self.collection_dropdown.pack(side=tk.LEFT, padx=5)
            self.collection_dropdown.bind('<<ComboboxSelected>>', lambda e: self.load_bookmarks())
            
            # Delete collection button
            self.delete_collection_btn = ttk.Button(
                collection_row,
                text="🗑️",
                width=3,
                command=self.delete_collection
            )
            self.delete_collection_btn.pack(side=tk.LEFT, padx=2)
            self.tooltip_manager.add_tooltip(self.delete_collection_btn, "Delete current collection")
            
            # New collection button in its own row
            button_row = ttk.Frame(collections_frame)
            button_row.pack(fill=tk.X, pady=(0,5))
            
            self.new_collection_btn = ttk.Button(
                button_row,
                text="➕ New Collection",
                command=self.create_new_collection
            )
            self.new_collection_btn.pack(side=tk.LEFT)
            
            # Separator
            ttk.Separator(self.bookmark_panel, orient=tk.HORIZONTAL).pack(fill=tk.X, padx=5, pady=5)
            
            # Bookmark toolbar
            self.bookmark_toolbar = ttk.Frame(self.bookmark_panel)
            self.bookmark_toolbar.pack(fill=tk.X, padx=5, pady=2)
            
            self.bookmark_clear_btn = ttk.Button(
                self.bookmark_toolbar,
                text="🗑️ Remove Selected",
                command=self.remove_selected_bookmarks
            )
            self.bookmark_clear_btn.pack(side=tk.LEFT)

            # Bookmark tree with all columns
            self.bookmark_tree = ttk.Treeview(
                self.bookmark_panel,
                columns=("Title", "Type", "Timestamp", "Notes"),
                show="headings",
                selectmode="extended"
            )
            
            # Configure columns
            for col in self.bookmark_tree["columns"]:
                self.bookmark_tree.heading(col, text=col)
                if col == "Type":
                    self.bookmark_tree.column(col, width=100)
                elif col == "Timestamp":
                    self.bookmark_tree.column(col, width=120)
                elif col == "Notes":
                    self.bookmark_tree.column(col, width=150)
                else:
                    self.bookmark_tree.column(col, width=200)
                    
            self.bookmark_tree.pack(fill=tk.BOTH, expand=True)
            self.bookmark_tree.bind('<Double-1>', self.open_bookmark_in_browser)
            self.bookmark_tree.bind('<Delete>', lambda e: self.remove_selected_bookmarks())
            self.bookmark_tree.bind('<Button-3>', self.show_bookmark_context_menu)
            self.bookmark_tree.bind('<Button-1>', self.handle_bookmark_click)
            
            # Enable drag and drop
            self.bookmark_tree.bind('<ButtonPress-1>', self.start_drag)
            self.bookmark_tree.bind('<B1-Motion>', self.drag)
            self.bookmark_tree.bind('<ButtonRelease-1>', self.drop)
            
            self.load_bookmarks()

            # Add tooltips
            self._add_tooltips()
            
        except Exception as e:
            self.logger.logger.error(f"Failed to create widgets: {str(e)}")
            self.error_manager.show_error('ui', f"Failed to create interface: {str(e)}")

    def _add_tooltips(self):
        """Add tooltips to all interactive elements"""
        try:
            # Video panel tooltips
            self.tooltip_manager.add_tooltip(
                self.video_tree,
                f"Double-click: Open in browser\n{self.symbols['bookmark']}: Bookmark\nDel: Remove"
            )
            
            # Playlist panel tooltips
            self.tooltip_manager.add_tooltip(
                self.playlist_tree,
                f"Double-click: Open in browser\n{self.symbols['bookmark']}: Bookmark\nDel: Remove"
            )
            
            # Bookmark panel tooltips
            self.tooltip_manager.add_tooltip(
                self.bookmark_tree,
                "Double-click: Open in browser\nDel: Remove"
            )
            
            # Toolbar button tooltips
            for btn in [self.video_clear_btn, self.playlist_clear_btn, self.bookmark_clear_btn]:
                self.tooltip_manager.add_tooltip(btn, "Remove selected items (Del)")
                
        except Exception as e:
            self.logger.logger.warning(f"Failed to add tooltips: {str(e)}")
            # Non-critical error, don't show to user

    def create_context_menus(self):
        """Create and configure context menus for all panels"""
        try:
            # Video context menu
            self.video_menu = tk.Menu(self.root, tearoff=0)
            self.video_menu.add_command(
                label="Toggle Bookmark (B)", 
                command=self.toggle_selected_video_bookmark
            )
            self.video_menu.add_command(
                label="Remove (Del)", 
                command=self.remove_selected_videos
            )
            
            # Playlist context menu
            self.playlist_menu = tk.Menu(self.root, tearoff=0)
            self.playlist_menu.add_command(
                label="Toggle Bookmark (B)", 
                command=self.toggle_selected_playlist_bookmark
            )
            self.playlist_menu.add_command(
                label="Remove (Del)", 
                command=self.remove_selected_playlists
            )
            
            # Bookmark context menu with collection submenu
            self.bookmark_menu = tk.Menu(self.root, tearoff=0)
            self.bookmark_menu.add_command(
                label="Edit Notes", 
                command=lambda: self.edit_bookmark_notes(self.bookmark_tree.focus())
            )
            self.bookmark_menu.add_command(
                label="Remove (Del)", 
                command=self.remove_selected_bookmarks
            )
            
            self.logger.info("Context menus created successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to create context menus: {str(e)}")
            self.error_manager.show_error('ui', f"Failed to create menus: {str(e)}")
            raise

    def bind_shortcuts(self):
        """Bind keyboard shortcuts"""
        try:
            # Global shortcuts
            self.root.bind('<Delete>', self.handle_delete_key)
            self.root.bind('b', self.handle_bookmark_key)
            self.root.bind('B', self.handle_bookmark_key)
            
            # Search shortcuts
            self.search_entry.bind('<Return>', lambda e: self.perform_search())
            
        except Exception as e:
            self.logger.error(f"Failed to bind shortcuts: {str(e)}")
            
    def handle_delete_key(self, event):
        """Handle Delete key press"""
        try:
            focused = self.root.focus_get()
            if focused == self.video_tree:
                self.remove_selected_videos()
            elif focused == self.playlist_tree:
                self.remove_selected_playlists()
            elif focused == self.bookmark_tree:
                self.remove_selected_bookmarks()
                
        except Exception as e:
            self.logger.error(f"Failed to handle delete key: {str(e)}")
            
    def handle_bookmark_key(self, event):
        """Handle Bookmark key press (B/b)"""
        try:
            focused = self.root.focus_get()
            if focused == self.video_tree:
                self.toggle_selected_video_bookmark()
            elif focused == self.playlist_tree:
                self.toggle_selected_playlist_bookmark()
                
        except Exception as e:
            self.logger.error(f"Failed to handle bookmark key: {str(e)}")

    def remove_selected_videos(self):
        """Removes selected video entries from the video treeview."""
        selected = self.video_tree.selection()
        for item_id in selected:
            self.video_tree.delete(item_id)

    def remove_selected_playlists(self):
        """Removes selected playlist entries from the playlist treeview."""
        selected = self.playlist_tree.selection()
        for item_id in selected:
            self.playlist_tree.delete(item_id)

    def remove_selected_bookmarks(self):
        """Removes selected bookmark entries from the bookmark treeview and storage."""
        selected = self.bookmark_tree.selection()
        for item_id in selected:
            values = self.bookmark_tree.item(item_id, 'values')
            title = values[0]
            for bookmark_id, data in self.bookmark_manager.bookmarks.items():
                if data["Title"] == title:
                    self.bookmark_manager.remove_bookmark(bookmark_id)
                    break
            self.bookmark_tree.delete(item_id)
        self.perform_search()  # Refresh panels to update bookmark status

    def toggle_selected_video_bookmark(self):
        """Toggles the bookmark status of the selected video in the video treeview."""
        item_id = self.video_tree.focus()
        if item_id:
            values = self.video_tree.item(item_id, 'values')
            title = values[0]
            video_id = values[5]
            if self.bookmark_manager.bookmarks.get(video_id):
                self.bookmark_manager.remove_bookmark(video_id)
            else:
                self.bookmark_manager.add_bookmark(video_id, {
                    "Title": title,
                    "Type": "video",
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Notes": ""
                })
            self.load_bookmarks()
            self.perform_search()

    def toggle_selected_playlist_bookmark(self):
        """Toggles the bookmark status of the selected playlist in the playlist treeview."""
        item_id = self.playlist_tree.focus()
        if item_id:
            values = self.playlist_tree.item(item_id, 'values')
            title = values[0]
            playlist_id = values[4]
            if self.bookmark_manager.bookmarks.get(playlist_id):
                self.bookmark_manager.remove_bookmark(playlist_id)
            else:
                self.bookmark_manager.add_bookmark(playlist_id, {
                    "Title": title,
                    "Type": "playlist",
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Notes": ""
                })
            self.load_bookmarks()
            self.perform_search()

    def on_playlist_select(self, event):
        """Handles playlist selection in the playlist treeview.
        
        Args:
            event: The UI event that triggered this action.
        """
        item_id = self.playlist_tree.focus()
        if not item_id:
            return
        values = self.playlist_tree.item(item_id, 'values')
        playlist_id = values[4]
        self.display_playlist_videos(playlist_id)

    def display_playlist_videos(self, playlist_id):
        """Displays videos from a selected playlist in the video panel.
        
        Args:
            playlist_id (str): The ID of the playlist whose videos are to be displayed.
        """
        # Clear video panel
        for item in self.video_tree.get_children():
            self.video_tree.delete(item)
        
        # Fetch and display playlist videos
        playlist_videos = self.api.get_playlist_videos(playlist_id)
        if playlist_videos and "items" in playlist_videos:
            video_data = []
            for item in playlist_videos["items"]:
                snippet = item["snippet"]
                video_id = item["snippet"]["resourceId"]["videoId"]
                is_bookmarked = self.bookmark_manager.bookmarks.get(video_id) is not None
                views = self._get_video_views(video_id)
                video_data.append([
                    snippet["title"],
                    snippet["channelTitle"],
                    views,
                    snippet["publishedAt"][:10],
                    self.symbols['bookmark'] if is_bookmarked else self.symbols['unbookmark'],
                    video_id
                ])
            
            df_videos = pd.DataFrame(video_data,
                                   columns=["Title", "Channel", "Views",
                                            "Upload Date", "Bookmark", "VideoId"])
            for row in df_videos.itertuples(index=False):
                self.video_tree.insert("", tk.END, values=row)

    def perform_search(self):
        """Execute search with performance logging and error handling
        
        Technical Requirements:
            - Response time: < 300ms for searches
            - Memory usage: < 100MB per search
            - Error recovery: Auto-retry on API failure
        """
        query = self.search_entry.get()
        mode = self.search_mode.get()
        
        if not query:
            return
            
        try:
            start_time = time.time()
            self.logger.log_user_action("search", f"Query: {query}, Mode: {mode}")
            
            # Clear previous results
            for tree in [self.video_tree, self.playlist_tree]:
                for item in tree.get_children():
                    tree.delete(item)
                    
            if mode in ("video", "both"):
                video_results = self.api.search_videos(query, max_results=10)
                if video_results and "items" in video_results:
                    self._populate_video_results(video_results)
                    
            if mode in ("playlist", "both"):
                playlist_results = self.api.search_playlists(query, max_results=10)
                if playlist_results and "items" in playlist_results:
                    self._populate_playlist_results(playlist_results)
                    
            duration = (time.time() - start_time) * 1000
              # Log performance metrics
            self.logger.log_performance("search", duration)
            
            # Log additional search metrics
            self.logger.logger.info(
                f"Search metrics - Query length: {len(query)}, "
                f"Mode: {mode}, Results: Video({len(self.video_tree.get_children())}), "
                f"Playlist({len(self.playlist_tree.get_children())})"
            )
            
            # Warn if performance threshold exceeded
            if duration > 300:  # 300ms threshold
                self.logger.logger.warning(f"Search performance threshold exceeded: {duration:.2f}ms")
            
        except Exception as e:
            self.logger.log_error(e, "Search failed")
            messagebox.showerror("Search Error", 
                               "Failed to perform search. Please try again later.")

    def _populate_video_results(self, results):
        """Populate video search results with error handling"""
        try:
            video_data = []
            for item in results["items"]:
                if item["id"]["kind"] == "youtube#video":
                    snippet = item["snippet"]
                    video_id = item["id"]["videoId"]
                    is_bookmarked = self.bookmark_manager.bookmarks.get(video_id) is not None
                    video_data.append([
                        snippet["title"],
                        snippet["channelTitle"],
                        "-",  # Views placeholder
                        snippet["publishedAt"][:10],
                        self.symbols['bookmark'] if is_bookmarked else self.symbols['unbookmark'],
                        video_id
                    ])
                    
            df_videos = pd.DataFrame(video_data, 
                                   columns=["Title", "Channel", "Views", 
                                          "Upload Date", "Bookmark", "VideoId"])
            for row in df_videos.itertuples(index=False):
                self.video_tree.insert("", tk.END, values=row)
                
        except Exception as e:
            self.logger.log_error(e, "Failed to populate video results")
            raise

    def _populate_playlist_results(self, results):
        """Populate playlist search results with error handling"""
        try:
            playlist_data = []
            for item in results["items"]:
                if item["id"]["kind"] == "youtube#playlist":
                    snippet = item["snippet"]
                    playlist_id = item["id"]["playlistId"]
                    is_bookmarked = self.bookmark_manager.bookmarks.get(playlist_id) is not None
                    video_count = self._get_playlist_video_count(playlist_id)
                    playlist_data.append([
                        snippet["title"],
                        snippet["channelTitle"],
                        video_count,
                        self.symbols['bookmark'] if is_bookmarked else self.symbols['unbookmark'],
                        playlist_id
                    ])
            
            df_playlists = pd.DataFrame(playlist_data,
                                     columns=["Title", "Channel", "Video Count", "Bookmark", "PlaylistId"])
            for row in df_playlists.itertuples(index=False):
                self.playlist_tree.insert("", tk.END, values=row)
                
        except Exception as e:
            self.logger.log_error(e, "Failed to populate playlist results")
            raise

    def open_video_in_browser(self, event):
        """Opens the selected video in a web browser.
        
        Args:
            event: The UI event that triggered this action.
        """
        item_id = self.video_tree.focus()
        if not item_id:
            return
        values = self.video_tree.item(item_id, 'values')
        video_id = values[5]
        url = f"https://www.youtube.com/watch?v={video_id}"
        webbrowser.open(url)

    def open_playlist_in_browser(self, event):
        """Opens the selected playlist in a web browser.
        
        Args:
            event: The UI event that triggered this action.
        """
        item_id = self.playlist_tree.focus()
        if not item_id:
            return
        values = self.playlist_tree.item(item_id, 'values')
        playlist_id = values[4]
        url = f"https://www.youtube.com/playlist?list={playlist_id}"
        webbrowser.open(url)

    def open_bookmark_in_browser(self, event):
        """Open the selected bookmark in browser"""
        item_id = self.bookmark_tree.focus()
        if not item_id:
            return
            
        try:
            # Get the ID stored in the tag
            item_tags = self.bookmark_tree.item(item_id)['tags']
            if not item_tags:
                return
                
            media_id = item_tags[0]
            item_type = self.bookmark_tree.item(item_id)['values'][1]  # Type column
            
            if item_type.lower() == "video":
                url = f"https://www.youtube.com/watch?v={media_id}"
            else:  # playlist
                url = f"https://www.youtube.com/playlist?list={media_id}"
                
            webbrowser.open(url)
            self.logger.logger.info(f"Opening {item_type} in browser: {url}")
            
        except Exception as e:
            self.error_manager.show_error('ui', f"Failed to open in browser: {str(e)}")
            self.logger.logger.error(f"Failed to open bookmark in browser: {str(e)}")

    def toggle_video_bookmark(self, event):
        """Toggle bookmark status for a video in the tree

        Technical Requirements:
            - Response time: < 100ms
            - Data consistency: Maintain sync between UI and storage
            - Error recovery: Preserve existing state on failure

        Args:
            event: The UI event that triggered this action

        Side Effects:
            - Updates bookmark icon in tree
            - Updates bookmark storage
            - Updates bookmark panel
        """
        region = self.video_tree.identify("region", event.x, event.y)
        if region != "cell":
            return
        col = self.video_tree.identify_column(event.x)
        if col != "#5":  # Bookmark column
            return
        item_id = self.video_tree.focus()
        if not item_id:
            return
        
        try:
            values = self.video_tree.item(item_id, 'values')
            title = values[0]
            video_id = values[5]
            
            # Store original state for recovery
            original_state = {
                'bookmarked': self.bookmark_manager.bookmarks.get(video_id) is not None,
                'tree_icon': self.video_tree.item(item_id)['values'][4],
                'tree_tags': self.video_tree.item(item_id)['tags']
            }
            
            try:
                if original_state['bookmarked']:
                    self.bookmark_manager.remove_bookmark(video_id)
                    self.video_tree.set(item_id, "Bookmark", self.symbols['unbookmark'])
                    self.video_tree.item(item_id, tags=())
                else:
                    self.bookmark_manager.add_bookmark(video_id, {
                        "Title": title,
                        "Type": "video",
                        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "Notes": ""
                    })
                    self.video_tree.set(item_id, "Bookmark", self.symbols['bookmark'])
                    self.video_tree.item(item_id, tags=('bookmarked',))
                    
                self.load_bookmarks()
                
            except Exception as e:
                # Restore original state on error
                if original_state['bookmarked']:
                    self.bookmark_manager.add_bookmark(video_id, {
                        "Title": title,
                        "Type": "video",
                        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "Notes": ""
                    })
                self.video_tree.set(item_id, "Bookmark", original_state['tree_icon'])
                self.video_tree.item(item_id, tags=original_state['tree_tags'])
                raise
                
        except Exception as e:
            self.error_manager.show_error('data', str(e))
            self.logger.logger.error(f"Failed to toggle video bookmark: {str(e)}")
            # Extra context for debugging
            self.logger.logger.debug(f"Video ID: {video_id}, Title: {title}")

    def toggle_playlist_bookmark(self, event):
        region = self.playlist_tree.identify("region", event.x, event.y)
        if region != "cell":
            return
        col = self.playlist_tree.identify_column(event.x)
        if col != "#4":  # Bookmark column
            return
        item_id = self.playlist_tree.focus()
        if not item_id:
            return
        values = self.playlist_tree.item(item_id, 'values')
        title = values[0]
        playlist_id = values[4]
        if self.bookmark_manager.bookmarks.get(playlist_id):
            self.bookmark_manager.remove_bookmark(playlist_id)
            self.playlist_tree.set(item_id, "Bookmark", self.symbols['unbookmark'])
            self.playlist_tree.item(item_id, tags=())
        else:
            self.bookmark_manager.add_bookmark(playlist_id, {
                "Title": title,
                "Type": "playlist",
                "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Notes": ""
            })
            self.playlist_tree.set(item_id, "Bookmark", self.symbols['bookmark'])
            self.playlist_tree.item(item_id, tags=('bookmarked',))
        self.load_bookmarks()

    def on_playlist_select(self, event):
        item_id = self.playlist_tree.focus()
        if not item_id:
            return
        values = self.playlist_tree.item(item_id, 'values')
        playlist_id = values[4]
        self.display_playlist_videos(playlist_id)

    def display_playlist_videos(self, playlist_id):
        # Clear video panel
        for item in self.video_tree.get_children():
            self.video_tree.delete(item)
        
        # Fetch and display playlist videos
        playlist_videos = self.api.get_playlist_videos(playlist_id)
        if playlist_videos and "items" in playlist_videos:
            video_data = []
            for item in playlist_videos["items"]:
                snippet = item["snippet"]
                video_id = item["snippet"]["resourceId"]["videoId"]
                is_bookmarked = self.bookmark_manager.bookmarks.get(video_id) is not None
                video_data.append([
                    snippet["title"],
                    snippet["channelTitle"],
                    "-",  # Views placeholder
                    snippet["publishedAt"][:10],
                    "★" if is_bookmarked else "☆",
                    video_id
                ])
            df_videos = pd.DataFrame(video_data, columns=["Title", "Channel", "Views", "Upload Date", "Bookmark", "VideoId"])
            for row in df_videos.itertuples(index=False):
                self.video_tree.insert("", tk.END, values=row)

    def perform_search(self):
        """Execute search with performance logging and error handling
        
        Technical Requirements:
            - Response time: < 300ms for searches
            - Memory usage: < 100MB per search
            - Error recovery: Auto-retry on API failure
        """
        query = self.search_entry.get()
        mode = self.search_mode.get()
        
        if not query:
            return
            
        try:
            start_time = time.time()
            self.logger.log_user_action("search", f"Query: {query}, Mode: {mode}")
            
            # Clear previous results
            for tree in [self.video_tree, self.playlist_tree]:
                for item in tree.get_children():
                    tree.delete(item)
                    
            if mode in ("video", "both"):
                video_results = self.api.search_videos(query, max_results=10)
                if video_results and "items" in video_results:
                    self._populate_video_results(video_results)
                    
            if mode in ("playlist", "both"):
                playlist_results = self.api.search_playlists(query, max_results=10)
                if playlist_results and "items" in playlist_results:
                    self._populate_playlist_results(playlist_results)
                    
            duration = (time.time() - start_time) * 1000
              # Log performance metrics
            self.logger.log_performance("search", duration)
            
            # Log additional search metrics
            self.logger.logger.info(
                f"Search metrics - Query length: {len(query)}, "
                f"Mode: {mode}, Results: Video({len(self.video_tree.get_children())}), "
                f"Playlist({len(self.playlist_tree.get_children())})"
            )
            
            # Warn if performance threshold exceeded
            if duration > 300:  # 300ms threshold
                self.logger.logger.warning(f"Search performance threshold exceeded: {duration:.2f}ms")
            
        except Exception as e:
            self.logger.log_error(e, "Search failed")
            messagebox.showerror("Search Error", 
                               "Failed to perform search. Please try again later.")

    def _populate_video_results(self, results):
        """Populate video search results with error handling"""
        try:
            video_data = []
            for item in results["items"]:
                if item["id"]["kind"] == "youtube#video":
                    snippet = item["snippet"]
                    video_id = item["id"]["videoId"]
                    is_bookmarked = self.bookmark_manager.bookmarks.get(video_id) is not None
                    video_data.append([
                        snippet["title"],
                        snippet["channelTitle"],
                        "-",  # Views placeholder
                        snippet["publishedAt"][:10],
                        self.symbols['bookmark'] if is_bookmarked else self.symbols['unbookmark'],
                        video_id
                    ])
                    
            df_videos = pd.DataFrame(video_data, 
                                   columns=["Title", "Channel", "Views", 
                                          "Upload Date", "Bookmark", "VideoId"])
            for row in df_videos.itertuples(index=False):
                self.video_tree.insert("", tk.END, values=row)
                
        except Exception as e:
            self.logger.log_error(e, "Failed to populate video results")
            raise

    def _populate_playlist_results(self, results):
        """Populate playlist search results with error handling"""
        try:
            playlist_data = []
            for item in results["items"]:
                if item["id"]["kind"] == "youtube#playlist":
                    snippet = item["snippet"]
                    playlist_id = item["id"]["playlistId"]
                    is_bookmarked = self.bookmark_manager.bookmarks.get(playlist_id) is not None
                    playlist_data.append([
                        snippet["title"],
                        snippet["channelTitle"],
                        "-",  # Video count placeholder
                        self.symbols['bookmark'] if is_bookmarked else self.symbols['unbookmark'],
                        playlist_id
                    ])
                    
            df_playlists = pd.DataFrame(playlist_data, 
                                     columns=["Title", "Channel", "Video Count", "Bookmark", "PlaylistId"])
            for row in df_playlists.itertuples(index=False):
                self.playlist_tree.insert("", tk.END, values=row)
                
        except Exception as e:
            self.logger.log_error(e, "Failed to populate playlist results")
            raise

    def open_video_in_browser(self, event):
        item_id = self.video_tree.focus()
        if not item_id:
            return
        values = self.video_tree.item(item_id, 'values')
        video_id = values[5]
        url = f"https://www.youtube.com/watch?v={video_id}"
        webbrowser.open(url)

    def open_playlist_in_browser(self, event):
        item_id = self.playlist_tree.focus()
        if not item_id:
            return
        values = self.playlist_tree.item(item_id, 'values')
        playlist_id = values[4]
        url = f"https://www.youtube.com/playlist?list={playlist_id}"
        webbrowser.open(url)

    def open_bookmark_in_browser(self, event):
        """Open the selected bookmark in browser"""
        item_id = self.bookmark_tree.focus()
        if not item_id:
            return
            
        try:
            # Get the ID stored in the tag
            item_tags = self.bookmark_tree.item(item_id)['tags']
            if not item_tags:
                return
                
            media_id = item_tags[0]
            item_type = self.bookmark_tree.item(item_id)['values'][1]  # Type column
            
            if item_type.lower() == "video":
                url = f"https://www.youtube.com/watch?v={media_id}"
            else:  # playlist
                url = f"https://www.youtube.com/playlist?list={media_id}"
                
            webbrowser.open(url)
            self.logger.logger.info(f"Opening {item_type} in browser: {url}")
            
        except Exception as e:
            self.error_manager.show_error('ui', f"Failed to open in browser: {str(e)}")
            self.logger.logger.error(f"Failed to open bookmark in browser: {str(e)}")

    def toggle_video_bookmark(self, event):
        """Toggle bookmark status for a video in the tree

        Technical Requirements:
            - Response time: < 100ms
            - Data consistency: Maintain sync between UI and storage
            - Error recovery: Preserve existing state on failure

        Args:
            event: The UI event that triggered this action

        Side Effects:
            - Updates bookmark icon in tree
            - Updates bookmark storage
            - Updates bookmark panel
        """
        region = self.video_tree.identify("region", event.x, event.y)
        if region != "cell":
            return
        col = self.video_tree.identify_column(event.x)
        if col != "#5":  # Bookmark column
            return
        item_id = self.video_tree.focus()
        if not item_id:
            return
        
        try:
            values = self.video_tree.item(item_id, 'values')
            title = values[0]
            video_id = values[5]
            
            # Store original state for recovery
            original_state = {
                'bookmarked': self.bookmark_manager.bookmarks.get(video_id) is not None,
                'tree_icon': self.video_tree.item(item_id)['values'][4],
                'tree_tags': self.video_tree.item(item_id)['tags']
            }
            
            try:
                if original_state['bookmarked']:
                    self.bookmark_manager.remove_bookmark(video_id)
                    self.video_tree.set(item_id, "Bookmark", self.symbols['unbookmark'])
                    self.video_tree.item(item_id, tags=())
                else:
                    self.bookmark_manager.add_bookmark(video_id, {
                        "Title": title,
                        "Type": "video",
                        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "Notes": ""
                    })
                    self.video_tree.set(item_id, "Bookmark", self.symbols['bookmark'])
                    self.video_tree.item(item_id, tags=('bookmarked',))
                    
                self.load_bookmarks()
                
            except Exception as e:
                # Restore original state on error
                if original_state['bookmarked']:
                    self.bookmark_manager.add_bookmark(video_id, {
                        "Title": title,
                        "Type": "video",
                        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "Notes": ""
                    })
                self.video_tree.set(item_id, "Bookmark", original_state['tree_icon'])
                self.video_tree.item(item_id, tags=original_state['tree_tags'])
                raise
                
        except Exception as e:
            self.error_manager.show_error('data', str(e))
            self.logger.logger.error(f"Failed to toggle video bookmark: {str(e)}")
            # Extra context for debugging
            self.logger.logger.debug(f"Video ID: {video_id}, Title: {title}")

    def toggle_playlist_bookmark(self, event):
        """Toggles the bookmark status for a playlist in the treeview.
        
        Args:
            event: The UI event that triggered this action.
        """
        region = self.playlist_tree.identify("region", event.x, event.y)
        if region != "cell":
            return
        col = self.playlist_tree.identify_column(event.x)
        if col != "#4":  # Bookmark column
            return
        item_id = self.playlist_tree.focus()
        if not item_id:
            return
        values = self.playlist_tree.item(item_id, 'values')
        title = values[0]
        playlist_id = values[4]
        if self.bookmark_manager.bookmarks.get(playlist_id):
            self.bookmark_manager.remove_bookmark(playlist_id)
            self.playlist_tree.set(item_id, "Bookmark", self.symbols['unbookmark'])
            self.playlist_tree.item(item_id, tags=())
        else:
            self.bookmark_manager.add_bookmark(playlist_id, {
                "Title": title,
                "Type": "playlist",
                "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Notes": ""
            })
            self.playlist_tree.set(item_id, "Bookmark", self.symbols['bookmark'])
            self.playlist_tree.item(item_id, tags=('bookmarked',))
        self.load_bookmarks()

    def create_new_collection(self):
        """Create a new bookmark collection"""
        try:
            # Create dialog
            dialog = tk.Toplevel(self.root)
            dialog.title("New Collection")
            dialog.transient(self.root)
            dialog.grab_set()
            
            # Center dialog
            dialog.geometry("300x120+%d+%d" % (
                self.root.winfo_rootx() + 50,
                self.root.winfo_rooty() + 50
            ))
            
            # Add widgets
            frame = ttk.Frame(dialog, padding="10")
            frame.pack(fill=tk.BOTH, expand=True)
            
            ttk.Label(frame, text="Collection Name:").pack(anchor=tk.W, pady=(0,5))
            name_entry = ttk.Entry(frame, width=30)
            name_entry.pack(fill=tk.X, pady=(0,10))
            
            def submit_collection():
                """Handles the submission of the new collection name."""
                name = name_entry.get().strip()
                if name:
                    if not any(c.lower() == name.lower() for c in self.bookmark_manager.get_collections()):
                        # Get selected bookmarks before creating collection
                        selected_items = self.bookmark_tree.selection()
                        selected_bookmark_ids = [
                            self.bookmark_tree.item(item)["tags"][0]
                            for item in selected_items
                        ]
                        
                        # Create new collection
                        self.bookmark_manager.add_collection(name)
                        
                        # Move selected bookmarks to new collection
                        for bookmark_id in selected_bookmark_ids:
                            if bookmark_id in self.bookmark_manager.bookmarks:
                                self.bookmark_manager.move_to_collection(bookmark_id, name)
                        
                        # Update UI
                        self.update_collections_dropdown()
                        self.collection_var.set(name)  # Select new collection
                        self.load_bookmarks()  # Refresh bookmarks display
                        dialog.destroy()
                    else:
                        messagebox.showerror("Error", "Collection already exists")
                else:
                    messagebox.showerror("Error", "Please enter a collection name")
                    
            button_frame = ttk.Frame(frame)
            button_frame.pack(fill=tk.X, pady=(0,5))
            
            ttk.Button(button_frame, text="Create", command=submit_collection).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.RIGHT)
            
            # Focus and bind keys
            name_entry.focus_set()
            dialog.bind('<Return>', lambda e: submit_collection())
            dialog.bind('<Escape>', lambda e: dialog.destroy())
            
        except Exception as e:
            self.logger.error(f"Failed to create collection dialog: {str(e)}")
            messagebox.showerror("Error", "Failed to create collection")

    def update_collections_dropdown(self):
        """Update the collections dropdown with current collections"""
        try:
            collections = ["All"] + sorted(list(self.bookmark_manager.get_collections()))
            self.collection_dropdown['values'] = collections
            if self.collection_var.get() not in collections:
                self.collection_var.set("All")
        except Exception as e:
            self.logger.error(f"Failed to update collections dropdown: {str(e)}")

    def load_bookmarks(self):
        """Load and display bookmarks in the bookmark panel"""
        try:
            # Clear current bookmarks
            for item in self.bookmark_tree.get_children():
                self.bookmark_tree.delete(item)
                
            # Get bookmarks
            bookmarks = self.bookmark_manager.get_all_bookmarks()
            selected_collection = self.collection_var.get()
            
            # Filter by collection
            if bookmarks and selected_collection != "All":
                bookmarks = {
                    k: v for k, v in bookmarks.items()
                    if v.get("Collection", "Default") == selected_collection
                }
                
            # Add to tree
            if bookmarks:
                for bookmark_id, data in bookmarks.items():
                    self.bookmark_tree.insert(
                        "",
                        tk.END,
                        values=(
                            data["Title"],
                            data["Type"],
                            data["Timestamp"],
                            data.get("Notes", ""),
                            data.get("Collection", "Default")
                        ),
                        tags=(bookmark_id,)
                    )
                    
        except Exception as e:
            self.logger.error(f"Failed to load bookmarks: {str(e)}")
            messagebox.showerror("Error", "Failed to load bookmarks")

    def handle_bookmark_click(self, event):
        """Handle clicks in the bookmark panel for editing notes"""
        try:
            region = self.bookmark_tree.identify("region", event.x, event.y)
            if region != "cell":
                return
                
            # Get clicked item and column
            item = self.bookmark_tree.identify_row(event.y)
            if not item:
                return
                
            column = self.bookmark_tree.identify_column(event.x)
            column_name = self.bookmark_tree.heading(column)["text"]
            
            if column_name == "Notes":
                # Visual feedback for clicking notes
                self.bookmark_tree.focus(item)
                self.bookmark_tree.selection_set(item)
                
                # Add brief highlight effect using AnimationManager
                self.animation_manager.flash_widget(self.bookmark_tree, item, "#e3f2fd", 200)
                
                # Open notes editor
                self.edit_bookmark_notes(item)
                
        except Exception as e:
            self.logger.error(f"Failed to handle bookmark click: {str(e)}")
            self.error_manager.show_error("UI Error", "Failed to handle bookmark click", details=str(e))

    def edit_bookmark_notes(self, item_id):
        """Show dialog to edit bookmark notes"""
        try:
            # Get current values
            values = self.bookmark_tree.item(item_id)["values"]
            title = values[0]
            current_notes = values[3] if len(values) > 3 else ""
            
            # Create dialog
            dialog = tk.Toplevel(self.root)
            dialog.title(f"Edit Notes - {title}")
            dialog.transient(self.root)
            dialog.grab_set()
            
            # Center dialog
            dialog.geometry("400x200+%d+%d" % (
                self.root.winfo_rootx() + 50,
                self.root.winfo_rooty() + 50
            ))
            
            # Add widgets
            frame = ttk.Frame(dialog, padding="10")
            frame.pack(fill=tk.BOTH, expand=True)
            
            # Notes field
            ttk.Label(frame, text="Notes:").pack(anchor=tk.W, pady=(0,5))
            
            notes_frame = ttk.Frame(frame)
            notes_frame.pack(fill=tk.BOTH, expand=True, pady=(0,10))
            
            notes_text = tk.Text(notes_frame, wrap=tk.WORD, width=40, height=6)
            notes_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            
            scrollbar = ttk.Scrollbar(notes_frame, orient=tk.VERTICAL, command=notes_text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            notes_text.configure(yscrollcommand=scrollbar.set)
            
            if current_notes:
                notes_text.insert("1.0", current_notes)
            notes_text.focus_set()
            
            # Buttons
            button_frame = ttk.Frame(frame)
            button_frame.pack(fill=tk.X, pady=(0,5))
            
            def save_notes():
                """Saves the edited notes for a bookmark."""
                new_notes = notes_text.get("1.0", "end-1c")
                bookmark_id = self.bookmark_tree.item(item_id)["tags"][0]
                if self.bookmark_manager.update_bookmark_notes(bookmark_id, new_notes):
                    self.load_bookmarks()
                    dialog.destroy()
                    
            def cancel():
                """Cancels the notes editing dialog."""
                dialog.destroy()
                
            ttk.Button(button_frame, text="Save", command=save_notes).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="Cancel", command=cancel).pack(side=tk.RIGHT)
            
            # Bind keyboard shortcuts
            dialog.bind('<Return>', lambda e: save_notes())
            dialog.bind('<Escape>', lambda e: cancel())
            
        except Exception as e:
            self.logger.error(f"Failed to edit notes: {str(e)}")
            messagebox.showerror("Error", "Failed to edit notes")

    def start_drag(self, event):
        """Starts a drag operation for reordering bookmarks.
        
        Args:
            event: The UI event that triggered this action.
        """
        try:
            self._drag_data = {"item": None, "x": 0, "y": 0}
            
            # Get the dragged item
            item = self.bookmark_tree.identify_row(event.y)
            if item:
                self._drag_data["item"] = item
                self._drag_data["x"] = event.x
                self._drag_data["y"] = event.y
                
        except Exception as e:
            self.logger.error(f"Failed to start drag: {str(e)}")
            
    def drag(self, event):
        """Handles the drag motion for reordering bookmarks.
        
        Args:
            event: The UI event that triggered this action.
        """
        try:
            if self._drag_data["item"]:
                # Show move cursor during drag
                self.root.config(cursor="exchange")
                
        except Exception as e:
            self.logger.error(f"Drag operation failed: {str(e)}")
            
    def drop(self, event):
        """Handles the drop operation for reordering bookmarks.
        
        Args:
            event: The UI event that triggered this action.
        """
        try:
            if self._drag_data["item"]:
                # Reset cursor
                self.root.config(cursor="")
                
                # Get source and target info
                source_item = self._drag_data["item"]
                target_item = self.bookmark_tree.identify_row(event.y)
                
                if source_item and target_item and source_item != target_item:
                    # Get source and target data
                    source_values = self.bookmark_tree.item(source_item)["values"]
                    target_values = self.bookmark_tree.item(target_item)["values"]
                    
                    # Move to new collection if target is a collection or has a collection
                    target_collection = target_values[4] if len(target_values) > 4 else "Default"
                    
                    # Get bookmark ID from tag
                    bookmark_id = self.bookmark_tree.item(source_item)["tags"][0]
                    
                    # Move bookmark to new collection
                    self.bookmark_manager.move_to_collection(bookmark_id, target_collection)
                    self.load_bookmarks()
                    
                self._drag_data["item"] = None
                
        except Exception as e:
            self.logger.error(f"Drop operation failed: {str(e)}")
            self.error_manager.show_error("ui", "Failed to move bookmark")

    def show_video_context_menu(self, event):
        """Shows the context menu for the video panel.
        
        Args:
            event: The UI event that triggered this action.
        """
        try:
            item = self.video_tree.identify_row(event.y)
            if item:
                self.video_tree.selection_set(item)
                self.video_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.logger.error(f"Failed to show video context menu: {str(e)}")

    def show_playlist_context_menu(self, event):
        """Shows the context menu for the playlist panel.
        
        Args:
            event: The UI event that triggered this action.
        """
        try:
            item = self.playlist_tree.identify_row(event.y)
            if item:
                self.playlist_tree.selection_set(item)
                self.playlist_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.logger.error(f"Failed to show playlist context menu: {str(e)}")

    def show_bookmark_context_menu(self, event):
        """Shows the context menu for the bookmark panel.
        
        Args:
            event: The UI event that triggered this action.
        """
        try:
            item = self.bookmark_tree.identify_row(event.y)
            if item:
                self.bookmark_tree.selection_set(item)
                self.bookmark_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.logger.error(f"Failed to show bookmark context menu: {str(e)}")

    def run(self):
        """Starts the main Tkinter event loop."""
        self.root.mainloop()

    def delete_collection(self):
        """Deletes the currently selected collection.
        
        Raises:
            ValueError: If "All" or "Default" collections are attempted to be deleted.
        """
        current_collection = self.collection_var.get()
        
        # Cannot delete All or Default collections
        if current_collection in ["All", "Default"]:
            messagebox.showwarning(
                "Cannot Delete Collection",
                "The 'All' and 'Default' collections cannot be deleted."
            )
            return
            
        # Confirm deletion
        if messagebox.askyesno(
            "Delete Collection",
            f"Are you sure you want to delete the collection '{current_collection}'?\n\n"
            "The bookmarks in this collection will be moved to the Default collection."
        ):
            try:
                # Get bookmarks in this collection
                bookmarks = {
                    k: v for k, v in self.bookmark_manager.get_all_bookmarks().items()
                    if v.get("Collection") == current_collection
                }
                
                # Move bookmarks to Default collection
                for bookmark_id in bookmarks:
                    self.bookmark_manager.move_to_collection(bookmark_id, "Default")
                
                # Remove the collection
                if self.bookmark_manager.remove_collection(current_collection):
                    # Update UI
                    self.collection_var.set("All")
                    self.update_collections_dropdown()
                    self.load_bookmarks()
                    messagebox.showinfo(
                        "Collection Deleted",
                        f"Collection '{current_collection}' has been deleted.\n"
                        "All bookmarks have been moved to the Default collection."
                    )
                else:
                    messagebox.showerror(
                        "Error",
                        f"Failed to delete collection '{current_collection}'"
                    )
                    
            except Exception as e:
                self.logger.error(f"Failed to delete collection: {str(e)}")
                messagebox.showerror(
                    "Error",
                    f"Failed to delete collection: {str(e)}"
                )

    def _get_video_views(self, video_id):
        """
        Fetches and formats the view count for a given video ID.
        """
        try:
            video_details = self.api.get_video_details(video_id)
            if video_details and "items" in video_details and video_details["items"]:
                views = int(video_details["items"][0]["statistics"].get("viewCount", 0))
                return f"{views:,}"  # Format with commas
            return "N/A"
        except Exception as e:
            self.logger.logger.error(f"Failed to get video views for {video_id}: {str(e)}")
            return "Error"

    def _get_playlist_video_count(self, playlist_id):
        """
        Fetches and formats the video count for a given playlist ID.
        """
        try:
            playlist_details = self.api.get_playlist_videos(playlist_id)
            if playlist_details and "pageInfo" in playlist_details:
                count = playlist_details["pageInfo"].get("totalResults", "N/A")
                return f"{count:,}" if isinstance(count, int) else str(count)
            return "N/A"
        except Exception as e:
            self.logger.logger.error(f"Failed to get playlist video count for {playlist_id}: {str(e)}")
            return "Error"
