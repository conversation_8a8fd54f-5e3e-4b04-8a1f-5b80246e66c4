import unittest
from data.bookmark_manager import BookmarkManager
import os
import json

class TestBookmarkManager(unittest.TestCase):
    def setUp(self):
        self.test_file = "test_bookmarks.json"
        self.manager = BookmarkManager(self.test_file)
        
    def tearDown(self):
        if os.path.exists(self.test_file):
            os.remove(self.test_file)
            
    def test_add_bookmark(self):
        item_id = "test_id"
        details = {"title": "Test Video", "url": "https://example.com"}
        self.manager.add_bookmark(item_id, details)
        self.assertIn(item_id, self.manager.bookmarks)
        self.assertEqual(self.manager.bookmarks[item_id], details)
        
    def test_remove_bookmark(self):
        item_id = "test_id"
        details = {"title": "Test Video", "url": "https://example.com"}
        self.manager.add_bookmark(item_id, details)
        self.manager.remove_bookmark(item_id)
        self.assertNotIn(item_id, self.manager.bookmarks)
        
    def test_add_collection(self):
        collection_name = "Test Collection"
        result = self.manager.add_collection(collection_name)
        self.assertTrue(result)
        self.assertIn(collection_name, self.manager.collections)
        
if __name__ == '__main__':
    unittest.main()
