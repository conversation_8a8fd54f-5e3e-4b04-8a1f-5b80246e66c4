# API Reference - YouTube Explorer Application

## 🔌 YouTube API Integration

### YouTubeAPI Class (`logic/youtube_api.py`)

The main interface for interacting with YouTube Data API v3.

#### Constructor
```python
api = YouTubeAPI()
```
- Automatically loads API key from environment variables
- Initializes cache manager for performance optimization
- Sets up error handling for API failures

#### Methods

##### `search_videos(query, max_results=10)`
Search for YouTube videos by keyword.

**Parameters:**
- `query` (str): Search query string
- `max_results` (int): Maximum number of results (default: 10)

**Returns:**
- `dict`: YouTube API response containing video data

**Example:**
```python
api = YouTubeAPI()
results = api.search_videos("Python tutorials", max_results=5)
for item in results["items"]:
    print(item["snippet"]["title"])
```

##### `search_playlists(query, max_results=10)`
Search for YouTube playlists by keyword.

**Parameters:**
- `query` (str): Search query string  
- `max_results` (int): Maximum number of results (default: 10)

**Returns:**
- `dict`: YouTube API response containing playlist data

**Example:**
```python
playlists = api.search_playlists("Machine Learning", max_results=3)
```

##### `get_playlist_videos(playlist_id)`
Retrieve all videos from a specific playlist.

**Parameters:**
- `playlist_id` (str): YouTube playlist ID

**Returns:**
- `dict`: API response with playlist video items

**Example:**
```python
videos = api.get_playlist_videos("PLrAXtmRdnEQy5Vgek-dqyXdNcgYiq6dHW")
```

##### `get_video_details(video_id)`
Get detailed information about a specific video.

**Parameters:**
- `video_id` (str): YouTube video ID

**Returns:**
- `dict`: Video details including statistics and snippet data

**Example:**
```python
details = api.get_video_details("dQw4w9WgXcQ")
views = details["items"][0]["statistics"]["viewCount"]
```

#### Error Handling
The API wrapper includes comprehensive error handling:
- **Rate limiting**: Automatic retry with exponential backoff
- **Network errors**: Graceful degradation with user notifications
- **Invalid responses**: Data validation and error reporting
- **API key issues**: Clear error messages for authentication problems

#### Caching System
- **Response caching**: API responses cached to reduce quota usage
- **Cache invalidation**: Automatic cache expiry for fresh data
- **Performance optimization**: Faster subsequent requests for same queries

---

## 📊 Data Management APIs

### BookmarkManager Class (`data/bookmark_manager.py`)

Handles persistent storage and management of user bookmarks.

#### Constructor
```python
manager = BookmarkManager(file_path="bookmarks.json")
```

#### Methods

##### `add_bookmark(item_id, data)`
Add a new bookmark to storage.

**Parameters:**
- `item_id` (str): Unique identifier (video/playlist ID)
- `data` (dict): Bookmark metadata

**Example:**
```python
manager.add_bookmark("dQw4w9WgXcQ", {
    "Title": "Never Gonna Give You Up",
    "Type": "video",
    "Timestamp": "2025-06-17 10:30:00",
    "Notes": "Classic Rick Roll"
})
```

##### `remove_bookmark(item_id)`
Remove a bookmark from storage.

**Parameters:**
- `item_id` (str): Bookmark identifier to remove

##### `get_bookmarks(collection=None)`
Retrieve bookmarks, optionally filtered by collection.

**Parameters:**
- `collection` (str, optional): Collection name filter

**Returns:**
- `dict`: Dictionary of bookmarks

##### `add_collection(name)`
Create a new bookmark collection.

**Parameters:**
- `name` (str): Collection name

##### `move_to_collection(item_id, collection_name)`
Move a bookmark to a specific collection.

**Parameters:**
- `item_id` (str): Bookmark identifier
- `collection_name` (str): Target collection name

---

## 🎨 UI Helper APIs

### SymbolManager Class (`utils/ui_helpers.py`)

Manages UI symbols with fallback support.

#### Methods

##### `get_symbol(symbol_type, fallback=None)`
Get a UI symbol with optional fallback.

**Parameters:**
- `symbol_type` (str): Symbol identifier ('bookmark', 'unbookmark', 'delete', etc.)
- `fallback` (str, optional): Fallback text if symbol unavailable

**Returns:**
- `str`: Symbol character or fallback text

**Example:**
```python
symbol_manager = SymbolManager()
bookmark_symbol = symbol_manager.get_symbol('bookmark', fallback='[*]')
```

### TooltipManager Class

Manages interactive tooltips for UI elements.

##### `add_tooltip(widget, text)`
Add a tooltip to a UI widget.

**Parameters:**
- `widget`: Tkinter widget object
- `text` (str): Tooltip text content

### ErrorManager Class

Centralized error handling and user notifications.

##### `show_error(error_type, message)`
Display an error dialog to the user.

**Parameters:**
- `error_type` (str): Error category ('api', 'ui', 'data')
- `message` (str): Error message to display

---

## 📝 Logging API

### AppLogger Class (`utils/logger.py`)

Application-wide logging system.

#### Methods

##### `log_user_action(action, details)`
Log user interactions for analytics.

**Parameters:**
- `action` (str): Action type ('search', 'bookmark', etc.)
- `details` (str): Additional action details

##### `log_performance(operation, duration_ms)`
Log performance metrics.

**Parameters:**
- `operation` (str): Operation name
- `duration_ms` (float): Operation duration in milliseconds

##### `log_error(exception, context)`
Log application errors with context.

**Parameters:**
- `exception` (Exception): Exception object
- `context` (str): Error context description

---

## 🔧 Configuration

### Environment Variables

Required environment variables in `.env` file:

```bash
# YouTube Data API v3 Key
API_KEY=your_youtube_api_key_here

# Optional: Logging level
LOG_LEVEL=INFO

# Optional: Cache duration (seconds)
CACHE_DURATION=3600
```

### API Rate Limits

YouTube Data API v3 quotas:
- **Default quota**: 10,000 units per day
- **Search operation**: 100 units per request
- **Video details**: 1 unit per video
- **Playlist items**: 1 unit per request

### Response Formats

#### Video Search Response
```json
{
  "items": [
    {
      "id": {"videoId": "string"},
      "snippet": {
        "title": "string",
        "channelTitle": "string",
        "publishedAt": "datetime",
        "description": "string"
      }
    }
  ]
}
```

#### Playlist Search Response
```json
{
  "items": [
    {
      "id": {"playlistId": "string"},
      "snippet": {
        "title": "string",
        "channelTitle": "string",
        "publishedAt": "datetime"
      }
    }
  ]
}
```

#### Bookmark Data Structure
```json
{
  "bookmarks": {
    "video_id": {
      "Title": "string",
      "Type": "video|playlist",
      "Timestamp": "ISO8601",
      "Notes": "string",
      "Collection": "string"
    }
  },
  "collections": {
    "collection_name": {
      "created": "ISO8601",
      "items": ["item_id1", "item_id2"]
    }
  }
}
```

---

## 🚨 Error Codes

### API Error Codes
- `400`: Bad Request - Invalid parameters
- `401`: Unauthorized - Invalid API key
- `403`: Forbidden - Quota exceeded
- `404`: Not Found - Resource doesn't exist
- `500`: Internal Server Error - YouTube API issue

### Application Error Codes
- `APP_001`: Missing API key
- `APP_002`: Invalid configuration
- `APP_003`: File system error
- `APP_004`: Network connectivity issue
- `APP_005`: Data corruption detected

---

*API Reference last updated: June 2025*
