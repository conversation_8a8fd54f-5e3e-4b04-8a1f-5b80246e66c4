# User Guide - Documentation Reader

## 🎯 Getting Started

Welcome to the **YouTube Explorer Documentation Reader**! This comprehensive guide will help you master all features of this beautiful Qt-based documentation browser.

## 📥 Installation & Setup

### Prerequisites
- **Python 3.6+** (recommended: Python 3.9 or 3.10)
- **PyQt5 5.15.0+** for the graphical interface
- **Operating System**: Windows, macOS, or Linux

### Quick Installation

#### Method 1: Automatic Setup (Recommended)
```bash
# Navigate to the application directory
cd docs_V0/doc_ReaderUI

# Run the smart launcher (handles dependencies automatically)
python launch.py
```

#### Method 2: Manual Setup
```bash
# Install PyQt5
pip install PyQt5>=5.15.0

# Run the application
python main.py
```

#### Method 3: Windows Batch File
```bash
# Double-click launch.bat or run:
launch.bat
```

### Troubleshooting Installation
If you encounter issues:
1. **Python not found**: Ensure Python is in your PATH
2. **PyQt5 installation fails**: Try `pip install --upgrade pip` first
3. **Permission errors**: Run as administrator (Windows) or use `sudo` (Linux/macOS)

## 🖥️ Interface Overview

### Main Window Layout
```
┌─────────────────────────────────────────────────────────────────┐
│ 📚 YouTube Explorer Documentation                              │
│ Interactive documentation browser and reader      [🔄] [📝]    │
├─────────────────────┬───────────────────────────────────────────┤
│ 📁 Documentation   │ 📄 Document Preview                      │
│ Files               │                                           │
│                     │ # Welcome to Documentation               │
│ ├── 📄 README.md    │                                           │
│ ├── 📄 API_DOC.md   │ This is a **beautiful** markdown         │
│ ├── 📄 USER_GUIDE   │ preview with:                             │
│ └── 📁 doc_ReaderUI │                                           │
│     ├── 📄 README   │ - Syntax highlighting                    │
│     └── 🐍 main.py  │ - Rich typography                         │
│                     │ - Professional styling                   │
│ 📊 README.md        │                                           │
│ 8KB • 245 lines     │ ```python                                │
│                     │ def hello_world():                        │
│                     │     print("Hello!")                       │
│                     │ ```                                       │
└─────────────────────┴───────────────────────────────────────────┘
│ Status: Ready                      📄 6 docs    📂 README.md   │
└─────────────────────────────────────────────────────────────────┘
```

### Interface Components

#### **Header Section**
- **Title**: Application name and description
- **Quick Actions**: 
  - 🔄 **Refresh**: Update file tree
  - 📝 **External**: Open current file in external editor

#### **File Explorer Panel (Left)**
- **Tree Navigation**: Hierarchical file structure
- **File Icons**: Visual file type identification
- **File Information**: Size and line count for markdown files
- **Smart Expansion**: Auto-expand folders with documentation

#### **Content Preview Panel (Right)**
- **Rich Markdown Rendering**: Beautiful HTML preview
- **Syntax Highlighting**: Code blocks with language detection
- **Responsive Layout**: Adapts to content and window size
- **Professional Typography**: Clean, readable text formatting

#### **Status Bar (Bottom)**
- **Application Status**: Current operation status
- **File Statistics**: Total documentation count
- **Current File**: Active file information

## 📁 Navigating Documentation

### File Tree Navigation

#### **Basic Navigation**
1. **Click** any file in the tree to preview it
2. **Expand/Collapse** folders by clicking the arrow icons
3. **Scroll** through the tree to find specific files
4. **Use keyboard arrows** for navigation

#### **File Type Recognition**
- 📄 **Markdown files** (.md) - Primary documentation
- 📝 **Text files** (.txt) - Plain text documents
- 🐍 **Python files** (.py) - Source code
- 📋 **JSON files** (.json) - Configuration files
- 📁 **Folders** - Directory containers

#### **Smart Features**
- **Auto-expansion**: Folders containing markdown files open automatically
- **File sizing**: Markdown files show size information
- **Bold highlighting**: Markdown files appear in bold text
- **Sorting**: Files and folders sorted alphabetically

### File Selection and Preview

#### **Selecting Files**
- **Single click**: Select and preview file
- **Double click**: Open file in external editor
- **Keyboard navigation**: Use arrow keys to navigate

#### **Preview Features**
- **Instant loading**: Files load immediately upon selection
- **Rich formatting**: Markdown rendered with professional styling
- **Code highlighting**: Syntax highlighting for code blocks
- **Responsive display**: Content adapts to panel size

## 📄 Markdown Preview Features

### Rich Text Rendering

#### **Headers and Typography**
- **H1 Headers**: 📚 Large headers with blue underline
- **H2 Headers**: 📖 Medium headers with blue underline  
- **H3-H6 Headers**: 📝 Smaller headers with emoji prefixes
- **Paragraphs**: Justified text with proper spacing
- **Line height**: Optimized for readability

#### **Text Formatting**
- **Bold text**: `**bold**` → **bold** (dark blue color)
- **Italic text**: `*italic*` → *italic* (gray color)
- **Inline code**: `` `code` `` → `highlighted code` (red text, gray background)
- **Links**: `[text](url)` → clickable links with hover effects

#### **Code Blocks**
```python
# Code blocks feature:
def example_function():
    """Syntax highlighted code blocks"""
    return "Beautiful formatting!"
```

Features:
- **Language detection**: Automatic syntax highlighting
- **Professional styling**: Clean, readable code presentation
- **Copy-friendly**: Easy to select and copy code
- **Scrollable**: Horizontal scroll for long lines

#### **Lists and Structure**
- **Bullet lists**: Clean bullet points with proper indentation
- **Numbered lists**: Sequential numbering with consistent spacing
- **Nested lists**: Support for multiple indentation levels
- **Blockquotes**: Styled quotes with left border and background

#### **Special Elements**
- **Horizontal rules**: `---` creates styled dividers
- **Emoji support**: Full emoji rendering in headers and content
- **Tables**: Clean table formatting (if present in markdown)

## 🎮 Keyboard Shortcuts

### Global Shortcuts
- **F5**: Refresh file tree
- **Ctrl+E**: Open current file in external editor
- **Ctrl+Q**: Exit application
- **Escape**: Close dialogs (if any)

### Navigation Shortcuts
- **Arrow Keys**: Navigate file tree
- **Enter**: Select highlighted file
- **Tab**: Move between interface elements
- **Page Up/Down**: Scroll content preview

### File Operations
- **Double-click**: Open file in external editor
- **Right-click**: Context menu (future feature)

## 🔧 Advanced Features

### External Editor Integration

#### **Opening Files Externally**
1. **Double-click** any file in the tree, OR
2. **Select file** and press **Ctrl+E**, OR
3. **Click the 📝 External button** in the header

#### **Supported Editors**
- **Text Editors**: Notepad++, Sublime Text, VS Code
- **IDEs**: PyCharm, Visual Studio, Atom
- **System Default**: Uses your system's default application

#### **Workflow Integration**
- **Edit and Preview**: Make changes externally, refresh to see updates
- **Seamless Switching**: Quick switching between reading and editing
- **File Association**: Respects your system's file associations

### File Information Display

#### **File Statistics**
When you select a file, the information panel shows:
- **File name** and type
- **File size** (bytes, KB, or MB)
- **Line count** (for text files)
- **Word count** (for markdown files)
- **Character count** and header count

#### **Real-time Updates**
- **Status bar**: Shows total documentation count
- **Current file**: Displays active file name
- **Application status**: Shows current operation

### Window Management

#### **Panel Resizing**
- **Drag the splitter** between panels to resize
- **Default ratio**: 30% file tree, 70% preview
- **Minimum sizes**: Panels have minimum width constraints
- **Responsive**: Layout adapts to window resizing

#### **Window Controls**
- **Minimize/Maximize**: Standard window controls
- **Resizing**: Smooth window resizing with content adaptation
- **Centering**: Application centers on screen at startup

## 🚨 Troubleshooting

### Common Issues

#### **Application Won't Start**
**Symptoms**: Error messages or application doesn't launch

**Solutions**:
1. **Check Python version**: Ensure Python 3.6+ is installed
2. **Install PyQt5**: Run `pip install PyQt5`
3. **Use launcher**: Try `python launch.py` for automatic setup
4. **Check permissions**: Ensure you have read access to documentation files

#### **Files Not Loading**
**Symptoms**: Blank preview or error messages

**Solutions**:
1. **Check file permissions**: Ensure files are readable
2. **Verify file encoding**: Files should be UTF-8 encoded
3. **File corruption**: Try opening file in text editor to verify content
4. **Refresh tree**: Press F5 to refresh file list

#### **Markdown Not Rendering**
**Symptoms**: Plain text instead of formatted content

**Solutions**:
1. **Check file extension**: Ensure file has .md extension
2. **Verify markdown syntax**: Check for syntax errors
3. **File encoding**: Ensure UTF-8 encoding
4. **Restart application**: Close and reopen the application

#### **External Editor Not Opening**
**Symptoms**: Nothing happens when trying to open externally

**Solutions**:
1. **Check file associations**: Ensure system has default app for file type
2. **File permissions**: Verify file is accessible
3. **Path issues**: Check if file path contains special characters
4. **System configuration**: Verify default applications are set

### Performance Issues

#### **Slow File Loading**
**Solutions**:
1. **Large files**: Very large files may take time to process
2. **System resources**: Close other applications to free memory
3. **File location**: Files on network drives may load slowly

#### **Interface Lag**
**Solutions**:
1. **Window size**: Try reducing window size
2. **System performance**: Check system resource usage
3. **Graphics drivers**: Ensure graphics drivers are updated

### Getting Help

#### **Error Information**
- **Status bar**: Check status bar for error messages
- **Console output**: Run from command line to see detailed errors
- **File logs**: Check for any log files created

#### **Reporting Issues**
When reporting problems, include:
- **Operating system** and version
- **Python version**: `python --version`
- **PyQt5 version**: `pip show PyQt5`
- **Error messages** or screenshots
- **Steps to reproduce** the issue

## 💡 Tips & Best Practices

### Efficient Usage

#### **Organization Tips**
- **Use the tree structure** to understand documentation hierarchy
- **Start with README.md** for project overview
- **Follow cross-references** between documents
- **Use external editor** for making changes

#### **Reading Strategies**
- **Scan headers** to understand document structure
- **Use code blocks** to understand implementation details
- **Follow links** to related documentation
- **Take advantage** of rich formatting for better comprehension

### Workflow Optimization

#### **Documentation Review**
1. **Start with overview** documents (README, PROJECT_OVERVIEW)
2. **Review architecture** documents for technical understanding
3. **Check API documentation** for implementation details
4. **Use user guides** for practical usage information

#### **Content Creation**
1. **Preview changes** immediately after editing
2. **Use external editor** for comfortable editing
3. **Refresh frequently** to see updates
4. **Check formatting** in the preview panel

## 🎓 Learning Path

### Beginner (First 10 minutes)
1. **Launch the application** using the launcher
2. **Explore the file tree** by clicking different files
3. **Preview markdown content** in the right panel
4. **Try keyboard shortcuts** like F5 and Ctrl+E

### Intermediate (Next 20 minutes)
1. **Resize panels** to your preference
2. **Open files externally** for editing
3. **Navigate using keyboard** shortcuts
4. **Understand file information** display

### Advanced (Ongoing)
1. **Integrate with your workflow** for documentation tasks
2. **Customize external editor** settings
3. **Use for documentation review** and validation
4. **Explore all markdown features** and formatting

---

## 🏁 Conclusion

The Documentation Reader provides a powerful, beautiful way to explore and preview markdown documentation. With its intuitive interface, rich markdown rendering, and seamless external editor integration, it enhances your documentation workflow significantly.

**Key Benefits:**
- ✅ **Beautiful interface** with professional dark theme
- ✅ **Rich markdown preview** with syntax highlighting
- ✅ **Intuitive navigation** with smart file tree
- ✅ **External editor integration** for seamless editing
- ✅ **Professional presentation** of documentation content

Start exploring your documentation with style and efficiency! 🚀

---

*User Guide v1.0 - June 17, 2025*
