# Workflow & Validation Rules

This directory contains the unified workflow and validation rules for the YouTube Explorer application.

## Directory Structure

```
.workflow/
├── workflow.yml         # Main workflow configuration
├── rules/              # Validation and implementation rules
│   └── validation_rules.md
├── validators/         # Validation scripts
│   ├── validate_code.ps1
│   ├── feature_validator.py
│   └── generate_report.py
└── reports/           # Generated validation reports
```

## Components

### 1. Workflow Configuration (workflow.yml)
- Defines CI/CD pipeline steps
- Configures validation environments
- Sets up test and validation processes

### 2. Validation Rules (rules/validation_rules.md)
- Feature implementation requirements
- Code quality standards
- Documentation requirements
- Performance metrics
- Error handling standards

### 3. Validators
- `validate_code.ps1`: Code quality and style validation
- `feature_validator.py`: Feature implementation validation
- `generate_report.py`: Validation report generation

## Usage

1. To run full validation:
```powershell
pwsh .workflow/validators/validate_code.ps1 -ValidateAll
```

2. To validate a specific feature:
```powershell
python .workflow/validators/feature_validator.py path/to/feature.yml
```

3. To generate a validation report:
```powershell
python .workflow/validators/generate_report.py path/to/results.json
```

## Integration

This workflow is automatically triggered on:
- Push to main/develop branches
- Pull request to main/develop
- Manual workflow dispatch

## Reports

Validation reports are generated in the `reports` directory and include:
- Code quality metrics
- Test coverage
- Documentation status
- Performance metrics
- Found issues and warnings
