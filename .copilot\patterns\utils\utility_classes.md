# Utility Class Patterns

## Error Manager Pat<PERSON>
```python
class ErrorManager:
    """Centralized error handling and messaging system."""
    
    ERROR_MESSAGES = {
        'file_not_found': "Could not find the specified file",
        'parse_error': "Failed to parse data",
        'network_error': "Network operation failed",
        # Add error types as needed
    }
    
    def __init__(self, root):
        self.root = root
    
    def show_error(self, error_type: str, detail: str = ""):
        """Show error dialog with consistent formatting."""
        message = self.ERROR_MESSAGES.get(error_type, "An error occurred")
        if detail:
            message = f"{message}\n\nDetails: {detail}"
        messagebox.showerror("Error", message)
        
    def log_error(self, error_type: str, exception: Exception):
        """Log error for debugging."""
        logging.error(f"{error_type}: {str(exception)}")
```

## Symbol Manager Pattern
```python
class SymbolManager:
    """Centralized Unicode symbol management."""
    
    SYMBOLS = {
        'play': '▶',
        'pause': '⏸',
        'bookmark': '🔖',
        'settings': '⚙',
        # Add symbols as needed
    }
    
    @classmethod
    def get(cls, name: str) -> str:
        """Get symbol by name with fallback."""
        return cls.SYMBOLS.get(name, '?')
```

## Tooltip Manager Pattern
```python
class TooltipManager:
    """Consistent tooltip implementation."""
    
    def __init__(self):
        self._tooltips = {}
    
    def add(self, widget, text: str, delay: int = 500):
        """Add tooltip to widget with consistent delay."""
        tooltip = ToolTip(widget, text, delay)
        self._tooltips[widget] = tooltip
        
    def remove(self, widget):
        """Clean up tooltip."""
        if widget in self._tooltips:
            self._tooltips[widget].hide()
            del self._tooltips[widget]
```
