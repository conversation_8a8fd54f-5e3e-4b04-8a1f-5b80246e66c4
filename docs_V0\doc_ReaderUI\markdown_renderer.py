"""
Markdown renderer for converting markdown files to HTML with syntax highlighting
"""

import re
from pathlib import Path
from typing import Dict, List

class MarkdownRenderer:
    """Renders markdown content to HTML with custom styling"""
    
    def __init__(self):
        self.css_styles = self._get_css_styles()
    
    def render_file(self, file_path: Path) -> str:
        """Render a markdown file to HTML"""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self.render_markdown(content)
            
        except Exception as e:
            return f"<p style='color: red;'>Error reading file: {str(e)}</p>"
    
    def render_markdown(self, content: str) -> str:
        """Convert markdown content to HTML"""
        
        # Start with CSS styles
        html = f"<html><head><style>{self.css_styles}</style></head><body>"
        
        # Process content line by line
        lines = content.split('\n')
        html += self._process_lines(lines)
        
        html += "</body></html>"
        
        return html
    
    def _process_lines(self, lines: List[str]) -> str:
        """Process markdown lines and convert to HTML"""
        
        html = ""
        in_code_block = False
        code_language = ""
        in_list = False
        list_level = 0
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # Handle code blocks
            if line.strip().startswith('```'):
                if not in_code_block:
                    # Start code block
                    in_code_block = True
                    code_language = line.strip()[3:].strip()
                    html += f'<div class="code-block"><pre><code class="language-{code_language}">'
                else:
                    # End code block
                    in_code_block = False
                    html += '</code></pre></div>'
                i += 1
                continue
            
            if in_code_block:
                # Inside code block - escape HTML and preserve formatting
                escaped_line = self._escape_html(line)
                html += escaped_line + '\n'
                i += 1
                continue
            
            # Handle headers
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                if level <= 6:
                    text = line[level:].strip()
                    # Add emoji and styling based on header level
                    if level == 1:
                        html += f'<h{level} class="header-1">{self._process_inline(text)}</h{level}>'
                    elif level == 2:
                        html += f'<h{level} class="header-2">{self._process_inline(text)}</h{level}>'
                    else:
                        html += f'<h{level} class="header-{level}">{self._process_inline(text)}</h{level}>'
                    i += 1
                    continue
            
            # Handle horizontal rules
            if line.strip() in ['---', '***', '___']:
                html += '<hr class="divider">'
                i += 1
                continue
            
            # Handle lists
            if re.match(r'^[\s]*[-*+]\s', line) or re.match(r'^[\s]*\d+\.\s', line):
                if not in_list:
                    in_list = True
                    list_level = len(line) - len(line.lstrip())
                    if re.match(r'^[\s]*\d+\.\s', line):
                        html += '<ol class="ordered-list">'
                    else:
                        html += '<ul class="unordered-list">'
                
                # Extract list item content
                if re.match(r'^[\s]*[-*+]\s', line):
                    content = re.sub(r'^[\s]*[-*+]\s', '', line)
                else:
                    content = re.sub(r'^[\s]*\d+\.\s', '', line)
                
                html += f'<li class="list-item">{self._process_inline(content)}</li>'
                i += 1
                continue
            else:
                if in_list:
                    # End list
                    in_list = False
                    if re.match(r'^[\s]*\d+\.\s', lines[i-1] if i > 0 else ''):
                        html += '</ol>'
                    else:
                        html += '</ul>'
            
            # Handle blockquotes
            if line.startswith('>'):
                quote_content = line[1:].strip()
                html += f'<blockquote class="blockquote">{self._process_inline(quote_content)}</blockquote>'
                i += 1
                continue
            
            # Handle empty lines
            if not line.strip():
                html += '<br>'
                i += 1
                continue
            
            # Regular paragraph
            html += f'<p class="paragraph">{self._process_inline(line)}</p>'
            i += 1
        
        # Close any open lists
        if in_list:
            html += '</ul>'
        
        return html
    
    def _process_inline(self, text: str) -> str:
        """Process inline markdown elements"""
        
        # Bold text
        text = re.sub(r'\*\*(.*?)\*\*', r'<strong class="bold">\1</strong>', text)
        text = re.sub(r'__(.*?)__', r'<strong class="bold">\1</strong>', text)
        
        # Italic text
        text = re.sub(r'\*(.*?)\*', r'<em class="italic">\1</em>', text)
        text = re.sub(r'_(.*?)_', r'<em class="italic">\1</em>', text)
        
        # Inline code
        text = re.sub(r'`(.*?)`', r'<code class="inline-code">\1</code>', text)
        
        # Links
        text = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2" class="link">\1</a>', text)
        
        # Escape remaining HTML
        text = self._escape_html_partial(text)
        
        return text
    
    def _escape_html(self, text: str) -> str:
        """Escape HTML characters"""
        
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))
    
    def _escape_html_partial(self, text: str) -> str:
        """Partially escape HTML (preserve our tags)"""
        
        # Only escape < and > that aren't part of our tags
        text = re.sub(r'<(?!/?(?:strong|em|code|a|span)\b)', '&lt;', text)
        text = re.sub(r'(?<!>)>(?![^<]*</(?:strong|em|code|a|span)>)', '&gt;', text)
        
        return text
    
    def _get_css_styles(self) -> str:
        """Get CSS styles for rendered HTML"""
        
        return """
        body {
            font-family: 'Segoe UI', 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: none;
            margin: 0;
            padding: 20px;
            background-color: #ffffff;
        }
        
        .header-1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 30px;
            margin-bottom: 20px;
            font-size: 2.2em;
            font-weight: bold;
        }
        
        .header-2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .header-3 {
            color: #34495e;
            margin-top: 20px;
            margin-bottom: 12px;
            font-size: 1.4em;
            font-weight: bold;
        }
        
        .header-4, .header-5, .header-6 {
            color: #34495e;
            margin-top: 15px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .paragraph {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        .bold {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .italic {
            font-style: italic;
            color: #34495e;
        }
        
        .inline-code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 2px 6px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            color: #e74c3c;
        }
        
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .code-block pre {
            margin: 0;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .code-block code {
            background: none;
            border: none;
            padding: 0;
            color: #2c3e50;
        }
        
        .unordered-list, .ordered-list {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        .list-item {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        
        .blockquote {
            border-left: 4px solid #3498db;
            margin: 15px 0;
            padding: 10px 20px;
            background-color: #f8f9fa;
            font-style: italic;
            color: #34495e;
        }
        
        .link {
            color: #3498db;
            text-decoration: none;
            border-bottom: 1px dotted #3498db;
        }
        
        .link:hover {
            color: #2980b9;
            border-bottom: 1px solid #2980b9;
        }
        
        .divider {
            border: none;
            height: 2px;
            background: linear-gradient(to right, #3498db, #2980b9, #3498db);
            margin: 30px 0;
        }
        
        /* Emoji and special character styling */
        h1:before { content: "📚 "; }
        h2:before { content: "📖 "; }
        h3:before { content: "📝 "; }
        
        /* Table styling (if needed) */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        """
