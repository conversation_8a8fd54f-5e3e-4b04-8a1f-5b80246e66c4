# API Documentation - YouTube Explorer V0

## 📚 Complete API Reference

This document provides comprehensive documentation for all classes, methods, and interfaces in the YouTube Explorer application.

## 🔌 YouTube API Integration

### YouTubeAPI Class (`logic/youtube_api.py`)

The main interface for interacting with YouTube Data API v3.

#### Constructor
```python
api = YouTubeAPI()
```
- Automatically loads API key from environment variables
- Initializes cache manager for performance optimization
- Sets up error handling for API failures

#### Core Methods

##### `search_videos(query: str, max_results: int = 10) -> dict`
Search for YouTube videos by keyword.

**Parameters:**
- `query` (str): Search query string
- `max_results` (int): Maximum number of results (default: 10)

**Returns:**
- `dict`: YouTube API response containing video data

**Example:**
```python
api = YouTubeAPI()
results = api.search_videos("Python tutorials", max_results=5)
for item in results["items"]:
    print(item["snippet"]["title"])
```

**Response Format:**
```json
{
  "items": [
    {
      "id": {"videoId": "string"},
      "snippet": {
        "title": "string",
        "channelTitle": "string",
        "publishedAt": "datetime",
        "description": "string"
      }
    }
  ]
}
```

##### `search_playlists(query: str, max_results: int = 10) -> dict`
Search for YouTube playlists by keyword.

**Parameters:**
- `query` (str): Search query string  
- `max_results` (int): Maximum number of results (default: 10)

**Returns:**
- `dict`: YouTube API response containing playlist data

**Example:**
```python
playlists = api.search_playlists("Machine Learning", max_results=3)
```

##### `get_playlist_videos(playlist_id: str, max_results: int = 10) -> dict`
Retrieve all videos from a specific playlist.

**Parameters:**
- `playlist_id` (str): YouTube playlist ID

**Returns:**
- `dict`: API response with playlist video items

**Example:**
```python
videos = api.get_playlist_videos("PLrAXtmRdnEQy5Vgek-dqyXdNcgYiq6dHW")
```

##### `get_video_details(video_id: str) -> dict`
Get detailed information about a specific video.

**Parameters:**
- `video_id` (str): YouTube video ID

**Returns:**
- `dict`: Video details including statistics and snippet data

**Example:**
```python
details = api.get_video_details("dQw4w9WgXcQ")
views = details["items"][0]["statistics"]["viewCount"]
```

#### Error Handling
The API wrapper includes comprehensive error handling:
- **Rate limiting**: Automatic retry with exponential backoff
- **Network errors**: Graceful degradation with user notifications
- **Invalid responses**: Data validation and error reporting
- **API key issues**: Clear error messages for authentication problems

#### Caching System
- **Response caching**: API responses cached to reduce quota usage
- **Cache invalidation**: Automatic cache expiry for fresh data
- **Performance optimization**: Faster subsequent requests for same queries

---

## 📊 Data Management APIs

### BookmarkManager Class (`data/bookmark_manager.py`)

Handles persistent storage and management of user bookmarks.

#### Constructor
```python
manager = BookmarkManager(storage_file="bookmarks.json")
```

#### Core Methods

##### `add_bookmark(item_id: str, details: dict, collection_name: str = "Default") -> None`
Add a new bookmark to storage.

**Parameters:**
- `item_id` (str): Unique identifier (video/playlist ID)
- `details` (dict): Bookmark metadata
- `collection_name` (str): Target collection name

**Example:**
```python
manager.add_bookmark("dQw4w9WgXcQ", {
    "Title": "Never Gonna Give You Up",
    "Type": "video",
    "Timestamp": "2025-06-17 10:30:00",
    "Notes": "Classic Rick Roll"
})
```

##### `remove_bookmark(item_id: str) -> None`
Remove a bookmark from storage.

**Parameters:**
- `item_id` (str): Bookmark identifier to remove

##### `get_all_bookmarks() -> dict`
Retrieve all bookmarks.

**Returns:**
- `dict`: Dictionary of all bookmarks

##### `get_collection_bookmarks(collection_name: str) -> dict`
Get bookmarks from a specific collection.

**Parameters:**
- `collection_name` (str): Collection name

**Returns:**
- `dict`: Bookmarks in the specified collection

##### `add_collection(name: str) -> None`
Create a new bookmark collection.

**Parameters:**
- `name` (str): Collection name

##### `remove_collection(collection_name: str) -> bool`
Remove a collection and move its bookmarks to Default.

**Parameters:**
- `collection_name` (str): Collection name to remove

**Returns:**
- `bool`: True if collection was removed, False otherwise

##### `move_to_collection(bookmark_id: str, collection: str) -> None`
Move a bookmark to a different collection.

**Parameters:**
- `bookmark_id` (str): Bookmark identifier
- `collection` (str): Target collection name

##### `update_bookmark_notes(item_id: str, notes: str) -> bool`
Update notes for a bookmark.

**Parameters:**
- `item_id` (str): Bookmark ID
- `notes` (str): New notes text

**Returns:**
- `bool`: True if updated successfully

#### Data Structure
```json
{
  "bookmarks": {
    "video_id": {
      "Title": "string",
      "Type": "video|playlist",
      "Timestamp": "ISO8601",
      "Notes": "string",
      "Collection": "string"
    }
  },
  "collections": {
    "collection_name": ["item_id1", "item_id2"]
  }
}
```

### CacheManager Class (`data/cache_manager.py`)

Provides file-based caching for API responses.

#### Constructor
```python
cache = CacheManager(cache_dir="cache")
```

#### Methods

##### `get_cached_response(api_method: str, params: dict) -> dict | None`
Retrieve cached API response if valid.

**Parameters:**
- `api_method` (str): API method name
- `params` (dict): API parameters

**Returns:**
- `dict | None`: Cached response or None if expired/missing

##### `cache_response(api_method: str, params: dict, response: dict) -> None`
Store API response in cache.

**Parameters:**
- `api_method` (str): API method name
- `params` (dict): API parameters
- `response` (dict): API response to cache

---

## 🎨 UI Helper APIs

### SymbolManager Class (`utils/ui_helpers.py`)

Manages UI symbols with fallback support.

#### Methods

##### `get_symbol(name: str, fallback: str = None) -> str`
Get a UI symbol with optional fallback.

**Parameters:**
- `name` (str): Symbol identifier ('bookmark', 'unbookmark', 'delete', etc.)
- `fallback` (str, optional): Fallback text if symbol unavailable

**Returns:**
- `str`: Symbol character or fallback text

**Example:**
```python
symbol_manager = SymbolManager()
bookmark_symbol = symbol_manager.get_symbol('bookmark', fallback='[*]')
```

**Available Symbols:**
- `bookmark`: ★ (fallback: *)
- `unbookmark`: ☆ (fallback: o)
- `delete`: 🗑️ (fallback: X)
- `add_collection`: 📁 (fallback: [+])
- `edit`: ✏️ (fallback: [E])
- `search`: 🔍 (fallback: [S])

### TooltipManager Class (`utils/ui_helpers.py`)

Manages interactive tooltips for UI elements.

#### Constructor
```python
tooltip_manager = TooltipManager(delay=300)
```

##### `add_tooltip(widget: tk.Widget, text: str) -> None`
Add a tooltip to a UI widget.

**Parameters:**
- `widget`: Tkinter widget object
- `text` (str): Tooltip text content

### ErrorManager Class (`utils/ui_helpers.py`)

Centralized error handling and user notifications.

#### Constructor
```python
error_manager = ErrorManager(parent_widget)
```

##### `show_error(error_type: str, message: str, details: str = None) -> None`
Display an error dialog to the user.

**Parameters:**
- `error_type` (str): Error category ('api', 'ui', 'data')
- `message` (str): Error message to display
- `details` (str, optional): Additional error details

##### `show_warning(title: str, message: str) -> None`
Show warning dialog.

##### `show_info(title: str, message: str) -> None`
Show info dialog.

### AnimationManager Class (`utils/ui_helpers.py`)

Manages UI animations and visual feedback.

#### Constructor
```python
animation_manager = AnimationManager(master, logger)
```

##### `flash_widget(treeview: ttk.Treeview, item_id: str, color: str, duration: int) -> None`
Flash a treeview item with specified color.

**Parameters:**
- `treeview`: Treeview widget
- `item_id`: Item identifier
- `color`: Flash color
- `duration`: Flash duration in milliseconds

---

## 📝 Logging API

### AppLogger Class (`utils/logger.py`)

Application-wide logging system.

#### Constructor
```python
logger = AppLogger(name="YouTubeExplorer")
```

#### Methods

##### `log_user_action(action: str, details: str = None) -> None`
Log user interactions for analytics.

**Parameters:**
- `action` (str): Action type ('search', 'bookmark', etc.)
- `details` (str): Additional action details

##### `log_performance(operation: str, duration_ms: float) -> None`
Log performance metrics.

**Parameters:**
- `operation` (str): Operation name
- `duration_ms` (float): Operation duration in milliseconds

##### `log_error(exception: Exception, context: str = None) -> None`
Log application errors with context.

**Parameters:**
- `exception` (Exception): Exception object
- `context` (str): Error context description

##### `log_api_call(method: str, success: bool, details: str = None) -> None`
Log API calls for monitoring.

**Parameters:**
- `method` (str): API method name
- `success` (bool): Call success status
- `details` (str): Additional call details

---

## 🔧 Configuration

### Environment Variables

Required environment variables in `.env` file:

```bash
# YouTube Data API v3 Key
API_KEY=your_youtube_api_key_here

# Optional: Logging level
LOG_LEVEL=INFO

# Optional: Cache duration (seconds)
CACHE_DURATION=3600
```

### API Rate Limits

YouTube Data API v3 quotas:
- **Default quota**: 10,000 units per day
- **Search operation**: 100 units per request
- **Video details**: 1 unit per video
- **Playlist items**: 1 unit per request

---

## 🚨 Error Codes

### API Error Codes
- `400`: Bad Request - Invalid parameters
- `401`: Unauthorized - Invalid API key
- `403`: Forbidden - Quota exceeded
- `404`: Not Found - Resource doesn't exist
- `500`: Internal Server Error - YouTube API issue

### Application Error Codes
- `APP_001`: Missing API key
- `APP_002`: Invalid configuration
- `APP_003`: File system error
- `APP_004`: Network connectivity issue
- `APP_005`: Data corruption detected

---

*API Documentation V0 - June 17, 2025*
